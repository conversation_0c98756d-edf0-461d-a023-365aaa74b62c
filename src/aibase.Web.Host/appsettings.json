﻿{
  "ConnectionStrings": {
    "Default": "Server=db-staging-aibase.postgres.database.azure.com;Database=aibase;User Id=db_staging_aibase;Password=AibaseMvp@2024s!;"
  },
  "AzureStorage": {
    "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=prodaibasestorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    "ContainerName": "images"
  },
  "env": "dev",
  "Hangfire": {
    "username": "admin",
    "password": "123qwe"
  },
  "App": {
    "ServerRootAddress": "http://localhost:8080/",
    "ClientRootAddress": "http://localhost:4200/",
    "CorsOrigins": "*",
    "FrontendUrl": "http://localhost:3000"
  },
  "Authentication": {
    "JwtBearer": {
      "IsEnabled": "true",
      "SecurityKey": "aibase_5E71A5905F744E42BE718640FE4C5E66",
      "Issuer": "aibase",
      "Audience": "aibase"
    }
  },
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://localhost:8080/"
      }
    }
  },
  "Swagger": {
    "ShowSummaries": false
  },
  "Debug": {
    "WebHook": false
  },
  "Email": {
    "SmtpHost": "smtp.mailgun.org",
    "SmtpPort": 587,
    "SmtpUsername": "<EMAIL>",
    "SmtpPassword": "**************************************************",
    "EnableSsl": true,
    "FromEmail": "<EMAIL>",
    "FromDisplayName": "FastGeo"
  },
  "Teams": {
    "WebhookUrl": "https://nobisoftvn.webhook.office.com/webhookb2/86b3bb10-9459-4c69-85a5-d5a2fabf0678@dfd263e5-5cf1-42c9-947c-2722e7018c6b/IncomingWebhook/938a90166b694a35be90d77150f66e57/c81dee5a-27ba-44d1-bc28-1df6f5326821/V2Z3BtjymSsncF4prkTT_h8dKgtfSh0Gue_TC3PD8hULI1"
  },
  "Sentry": {
    "Dsn": "https://<EMAIL>/4509405524000768",
    "IncludeSourceContext": true,
    "Environment": "Development",
    "TracesSampleRate": 1.0,
    "ProfilesSampleRate": 1.0,
    "SendDefaultPii": true,
    "AttachStacktrace": true,
    "Debug": true
  },
  "ExternalServices": {
    "Ocr": {
      "BaseIpAddress": "*************",
      "SubscriptionKey": "8daff73adafa4a009436a6fa77694346",
      "AzureEndpoint": "https://ocr-deployment.cognitiveservices.azure.com/"
    },
    "Segment": {
      "BaseIpAddress": "*************",
      "PredictionKey": "8d25ab5a7d174a42b3be152ad9ef9bfa",
      "CustomVisionImageEndpoint": "https://aibasecustomvision-prediction.cognitiveservices.azure.com/customvision/v3.0/Prediction/1f02c128-bc4a-4a0c-820a-079a52a85152/detect/iterations/Iteration2/image",
      "CustomVisionUrlEndpoint": "https://aibasecustomvision-prediction.cognitiveservices.azure.com/customvision/v3.0/Prediction/1f02c128-bc4a-4a0c-820a-079a52a85152/detect/iterations/Iteration2/url"
    },
    "Workflow": {
      "BaseIpAddress": "*************"
    }
  }
}