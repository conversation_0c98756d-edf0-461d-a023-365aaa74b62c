﻿using Abp.Domain.Entities.Auditing;
using Abp.Domain.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using aibase.FileEntity;
using aibase.ProjectEntity;
using aibase.DrillHoleEntity;
using aibase.ImageCrops;
using aibase.ImageSubtypes;
using aibase.ImageTypes;
using aibase.Prospects;
using Microsoft.EntityFrameworkCore;

namespace aibase.ImageEntity
{
    [Index(nameof(ProjectId))]
    [Index(nameof(ProspectId))]
    [Index(nameof(DrillHoleId), nameof(ImageClass), nameof(Type))]
    public class Image : Entity<int>, IHasCreationTime, IHasModificationTime
    {
        public ICollection<File> Files { get; set; } = [];
        
        public ICollection<ImageCrop> CroppedImages { get; set; } = [];
        
        public ImageClass ImageClass { get; set; }
        
        [Required]
        public ImageCategory Type { get; set; }
        
        public StandardType? StandardType { get; set; }

        [Required]
        public int DrillHoleId { get; set; }
        public virtual DrillHole DrillHole { get; set; }
        
        [Required]
        public int ProjectId { get; set; }
        public virtual Project Project { get; set; }        
        
        [Required]
        public int ProspectId { get; set; }
        public virtual Prospect Prospect { get; set; }
        
        [Required]
        public double DepthFrom { get; set; }

        [Required]
        public double DepthTo { get; set; }
        
        public string? OcrResult { get; set; }
        
        public string? DirectOcrResult { get; set; }
        
        public string? SegmentResult { get; set; }
        
        public string? SegmentDetailResult { get; set; }
        
        public ImageStatus? ImageStatus { get; set; }
        
        public string? BoundingBox { get; set; }
        
        public string? BoundingRows { get; set; }
        
        public string? DrillholeNameOcr {  get; set; }

        public string? OriginalDirectOcrResult { get; set; }
        
        public string? OriginalSegmentResult { get; set; }
        
        public string? OriginalBoundingBox { get; set; }
        
        public string? OriginalBoundingRows { get; set; }
        
        public string? OriginalDrillholeNameOcr { get; set; }
        
        public string? OriginalFractures { get; set; }
        
        public double? DepthFromOcr { get; set; }
        
        public double? DepthToOcr { get; set; }
        
        public string? Fractures { get; set; }
        
        public ImageCategoryNew ImageCategory { get; set; }
        
        public int? ImageTypeId { get; set; }
        public virtual ImageType? ImageType { get; set; }

        public int? ImageSubtypeId { get; set; }
        public virtual ImageSubtype? ImageSubtype { get; set; }
        
        public DateTime CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
        
        public string? CreatedByUser { get; set; }
        
        public string? CreatedByName { get; set; }
    }

    public enum ImageTypeUpload
    {
        Original = 1,
        Cropped,
        CroppedRow,
        Segmented,
        Mask
    }

    public enum ImageClass
    {
        Drilling = 1,
        Map,
        General
    }

    public enum ImageCategory
    {
        Standard = 1,
        Hyperspectral = 2,
        Optical = 3,
        AtRig = 5
    }
    
    public enum ImageCategoryNew
    {
        Drilling = 1,
        Map,
        General
    }

    public enum StandardType
    {
        Dry = 1,
        Wet,
        Uv
    }

    public enum ImageStatus
    {
        NotStarted = 1,
        InProgress,
        Reprocess,
        Review,
        Complete,
        Unnamed
    }
}
