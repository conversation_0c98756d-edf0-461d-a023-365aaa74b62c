using System.ComponentModel.DataAnnotations;

namespace aibase.Configuration
{
    /// <summary>
    /// Configuration options for external service endpoints
    /// </summary>
    public class ExternalServiceOptions
    {
        public const string SectionName = "ExternalServices";

        /// <summary>
        /// OCR service endpoints configuration
        /// </summary>
        [Required]
        public OcrServiceEndpoints Ocr { get; set; } = new();

        /// <summary>
        /// Segment service endpoints configuration
        /// </summary>
        [Required]
        public SegmentServiceEndpoints Segment { get; set; } = new();

        /// <summary>
        /// Workflow service endpoints configuration
        /// </summary>
        [Required]
        public WorkflowServiceEndpoints Workflow { get; set; } = new();
    }

    /// <summary>
    /// OCR service endpoint configurations
    /// </summary>
    public class OcrServiceEndpoints
    {
        /// <summary>
        /// Base IP address for OCR services
        /// </summary>
        [Required]
        public string BaseIpAddress { get; set; } = string.Empty;

        /// <summary>
        /// Analyze endpoint (port 6000)
        /// </summary>
        public string AnalyzeEndpoint => $"http://{BaseIpAddress}:6000/analyze";

        /// <summary>
        /// Generate polygon endpoint (port 7000)
        /// </summary>
        public string GeneratePolygonEndpoint => $"http://{BaseIpAddress}:7000/generate-polygon";

        /// <summary>
        /// Process image endpoint (port 8000)
        /// </summary>
        public string ProcessImageEndpoint => $"http://{BaseIpAddress}:8000/process-image";

        /// <summary>
        /// Detect fractures endpoint (port 9000)
        /// </summary>
        public string DetectFracturesEndpoint => $"http://{BaseIpAddress}:9000/detect-fractures";

        /// <summary>
        /// Azure Computer Vision subscription key
        /// </summary>
        [Required]
        public string SubscriptionKey { get; set; } = string.Empty;

        /// <summary>
        /// Azure Computer Vision endpoint
        /// </summary>
        [Required]
        public string AzureEndpoint { get; set; } = string.Empty;
    }

    /// <summary>
    /// Segment service endpoint configurations
    /// </summary>
    public class SegmentServiceEndpoints
    {
        /// <summary>
        /// Base IP address for segment services
        /// </summary>
        [Required]
        public string BaseIpAddress { get; set; } = string.Empty;

        /// <summary>
        /// Segment GPU endpoint (port 5000)
        /// </summary>
        public string SegmentGpuEndpoint => $"http://{BaseIpAddress}:5000/predict-auto";

        /// <summary>
        /// Azure Custom Vision prediction key
        /// </summary>
        [Required]
        public string PredictionKey { get; set; } = string.Empty;

        /// <summary>
        /// Azure Custom Vision image endpoint
        /// </summary>
        [Required]
        public string CustomVisionImageEndpoint { get; set; } = string.Empty;

        /// <summary>
        /// Azure Custom Vision URL endpoint
        /// </summary>
        [Required]
        public string CustomVisionUrlEndpoint { get; set; } = string.Empty;
    }

    /// <summary>
    /// Workflow service endpoint configurations
    /// </summary>
    public class WorkflowServiceEndpoints
    {
        /// <summary>
        /// Base IP address for workflow services
        /// </summary>
        [Required]
        public string BaseIpAddress { get; set; } = string.Empty;

        /// <summary>
        /// Process endpoint (port 8386)
        /// </summary>
        public string ProcessEndpoint => $"http://{BaseIpAddress}:8386/process";

        /// <summary>
        /// Segment detail SAM endpoint (port 8388)
        /// </summary>
        public string SegmentDetailSamEndpoint => $"http://{BaseIpAddress}:8388/process_sam";
        
        /// <summary>
        /// Segment detail SAM endpoint (port 8388)
        /// </summary>
        public string SegmentCropEndpoint => $"http://{BaseIpAddress}:8388/segment_crop";

        /// <summary>
        /// Segment detail core pieces endpoint (port 8387)
        /// </summary>
        public string SegmentDetailCorePiecesEndpoint => $"http://{BaseIpAddress}:8387/process_core_pieces";
    }
}
