using System;
using Abp.Domain.Entities;
using Abp.Domain.Entities.Auditing;
using aibase.DrillHoleEntity;

namespace aibase.TrayDepthResults;

public class TrayDepthResult : Entity, IHasCreationTime, IHasModificationTime
{
    public int DrillHoleId { get; set; }
    public virtual DrillHole DrillHole { get; set; }
    
    public int TrayNumber { get; set; }
    public double StartDepth { get; set; }
    public double EndDepth { get; set; }
    
    public DateTime CreationTime { get; set; }
    public DateTime? LastModificationTime { get; set; }
}