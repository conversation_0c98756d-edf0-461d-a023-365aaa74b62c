﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.UI;
using aibase.DrillHoles.Services.DrillHoleService;
using aibase.ImageCrops;
using aibase.ImageEntity;
using aibase.Images.Dto;
using aibase.Images.Services.AlignService;
using aibase.Images.Services.CropService;
using aibase.Images.Services.ProcessService.Handler;
using aibase.Images.Services.RotateService;
using aibase.Images.Services.UploadService.Handler;
using aibase.Models.Dto;
using aibase.Models.Services.OcrService;
using aibase.Models.Services.SegmentService;
using aibase.Models.Services.WorkflowService;
using aibase.ResultSteps;
using aibase.RockLines.Dto;
using aibase.RockLines.Services;
using aibase.StepWorkflowEntity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace aibase.Images.Services.ProcessService
{
    /// <inheritdoc />
    public class ProcessService : IProcessService
    {
        private readonly IRepository<Image, int> _repository;
        private readonly IRepository<StepWorkflow, int> _stepWorkflowRepository;
        private readonly IRepository<ResultStep, int> _resultStepRepository;
        private readonly IUnitOfWorkManager _unitOfWorkManager;
        private readonly IOcrService _ocrService;
        private readonly ICropService _cropService;
        private readonly ISegmentService _segmentService;
        private readonly IRotateService _rotateService;
        private readonly IAlignService _alignService;
        private readonly IDrillHoleService _drillHoleService;
        private readonly IWorkflowService _workflowService;
        private readonly IRockLineService _rockLineService;

        /// <summary>
        /// 
        /// </summary>
        public ProcessService(
            IRepository<Image, int> repository,
            IRepository<StepWorkflow> stepWorkflowRepository,
            IRepository<ResultStep> resultStepRepository,
            IUnitOfWorkManager unitOfWorkManager,
            IOcrService ocrService,
            ICropService cropService,
            ISegmentService segmentService,
            IRotateService rotateService,
            IAlignService alignService,
            IDrillHoleService drillHoleService, 
            IWorkflowService workflowService, IRockLineService rockLineService)
        {
            _repository = repository;
            _stepWorkflowRepository = stepWorkflowRepository;
            _resultStepRepository = resultStepRepository;
            _unitOfWorkManager = unitOfWorkManager;
            _ocrService = ocrService;
            _cropService = cropService;
            _segmentService = segmentService;
            _rotateService = rotateService;
            _alignService = alignService;
            _drillHoleService = drillHoleService;
            _workflowService = workflowService;
            _rockLineService = rockLineService;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="UserFriendlyException"></exception>
        public async Task<IActionResult> ProcessImageV2(ProcessImageDto input)
        {
            try
            {
                var steps = await _stepWorkflowRepository.GetAllIncluding(x => x.BoundingBox, x => x.BoundingRows)
                    .AsNoTracking()
                    .Where(x => x.WorkflowId == input.WorkflowId)
                    .OrderBy(x => x.CreationTime)
                    .ToListAsync();
                if (steps.Count == 0)
                {
                    throw new UserFriendlyException("The workflow has not been configured with steps.");
                }

                var image = await _repository.GetAllIncluding(x => x.Files, x => x.CroppedImages)
                    .FirstOrDefaultAsync(x => x.Id == input.ImageId);
                if (image == null)
                {
                    throw new UserFriendlyException("The image has not been configured with steps.");
                }

                var fileImg = image.Files.FirstOrDefault(x => x.Size == ImageConstSettings.ImageMediumSize) ??
                              image.Files.FirstOrDefault(x => x.Size == ImageConstSettings.ImageFullSize);

                if (fileImg == null)
                {
                    throw new UserFriendlyException("This image currently does not have a URL path.");
                }
                var inputStep = fileImg.Url;

                var boundingBoxDeserialize = JsonConvert.DeserializeObject<CropCoordinate[]>(image.BoundingBox ?? "[]") ?? [];
                var boundingRowDeserialize = JsonConvert.DeserializeObject<CropCoordinate[]>(image.BoundingRows ?? "[]") ?? [];
                
                var boundingBox = boundingBoxDeserialize.OrderBy(c => c.Y).ToArray();
                var boundingRow = boundingRowDeserialize.OrderBy(c => c.Y).ToArray();
                var cropCoordinates = boundingBox.Concat(boundingRow).OrderBy(c => c.Y).ToArray();

                var cropResult = new List<ImageCrop>();

                foreach (var step in steps)
                {
                    switch (step.ToolType)
                    {
                        case ToolType.AssignBox:
                            boundingBox = JsonConvert.DeserializeObject<CropCoordinate[]>(step.BoundingBox?.Coordinates ?? "[]")?
                                .OrderBy(c => c.Y).ToArray();
                            cropCoordinates = boundingBox?.Concat(boundingRow ?? []).OrderBy(c => c.Y).ToArray();
                            break;
                        case ToolType.AssignRow:
                            boundingRow = JsonConvert.DeserializeObject<CropCoordinate[]>(step.BoundingRows?.Coordinates ?? "[]")?
                                .OrderBy(c => c.Y).ToArray();
                            cropCoordinates = boundingBox?.Concat(boundingRow ?? []).OrderBy(c => c.Y).ToArray();
                            break;
                    }

                    if (step.ToolType == ToolType.Crop)
                    {
                        var check = steps.Select(x => x.ToolType).Contains(ToolType.AssignBox);
                        cropResult = await HandleCropStep(step, input, inputStep, cropCoordinates ?? [], image.DrillHoleId,
                            check);
                        
                        // Rock Line
                        var segmentResult = JsonConvert.DeserializeObject<List<SegmentResultDto>>(image.SegmentResult ?? "[]")?.ToList() ??
                                            [];
                        var autoInsertRockLineDto = new AutoInsertRockLineDto
                        {
                            Segments = segmentResult,
                            SegmentDetails = segmentResult,
                            ImageCrops = cropResult
                        };
                        await _rockLineService.AutoInsertRockLineAsync(autoInsertRockLineDto);
                    }

                    switch (step.ToolType)
                    {
                        case ToolType.RotateClockwise or ToolType.RotateCounterClockwise:
                            await _rotateService.RotateImg(image, step.DataValue, cropCoordinates ?? [], step.ToolType);
                            break;
                        case ToolType.Align:
                        {
                            var alignPolygon = new AlignPolygonDto
                            {
                                Id = input.ImageId,
                                Coordinate = null
                            };
                            await _alignService.AlignRows(alignPolygon);
                            break;
                        }
                    }

                    switch (step.ModelId)
                    {
                        case AiModelType.Ocr: //
                            await HandleOcrStep(step, input, image, inputStep, boundingRow ?? []);
                            break;
                        case AiModelType.Segment: //
                            await HandleSegmentStep(step, input, image, inputStep, boundingRow ?? []);
                            break;
                        case AiModelType.DirectOcr: //
                            await HandleDirectOcrStep(step, input, image, inputStep, cropResult, boundingRow ?? []);
                            break;
                        case AiModelType.GeneratePolygon: //
                            await HandlePolygonStep(step, input, image, inputStep);
                            break;
                        case AiModelType.PredictDrillholeAndDepth: //
                            await HandlePredictDrillholeAndDepthStep(step, input, image, inputStep);
                            break;
                        case AiModelType.DetectFractures:
                            await HandleDetectFractures(step, input, image, inputStep);
                            break;
                        case AiModelType.NewSegment: //
                            await HandleSegmentStep(step, input, image, inputStep, boundingRow ?? []);
                            break;
                        case AiModelType.PolygonCropOcrSegment: // 8
                            await HandlePolygonCropOcrSegmentStep(step, image, inputStep, input.ImageStatus);
                            break;
                        case AiModelType.SegmentationDetail: // 10
                            await HandleSegmentationDetailStep(step, image, inputStep, input.ImageStatus);
                            break;
                        case AiModelType.PolygonCrop: // 11
                            await HandlePolygonCropStep(step, image, inputStep, input.ImageStatus);
                            break;
                        case AiModelType.PolygonCropOcrSegmentDetailSam: // 12
                            await HandlePolygonCropOcrSegmentDetailSamStep(step, image, inputStep, input.ImageStatus);
                            break;
                        case AiModelType.PolygonCropOcrSegmentDetailCorePieces: // 13
                            await HandlePolygonCropOcrSegmentDetailCorePiecesStep(step, image, inputStep, input.ImageStatus);
                            break;
                        case AiModelType.SegmentAutoCrop: // 14
                            await HandleSegmentAutoCropStepAsync(step, image, inputStep, input.ImageStatus);
                            break;
                    }
                }

                return new OkObjectResult(new { Message = "The workflow has been executed successfully" });
            }
            catch (UserFriendlyException ex)
            {
                return new BadRequestObjectResult(ex.Message);
            }
            catch (ArgumentException ex)
            {
                return new BadRequestObjectResult(ex.Message);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        public async Task HandlePolygonStep(StepWorkflow step, ProcessImageDto input, Image image, string inputStep)
        {
            var polygonResult = await _ocrService.PredictPolygon(null, inputStep);

            await SaveResultStep(step, input.ImageId, inputStep, polygonResult);

            var polygonBox = polygonResult.Where(c => c.Type == ImageConstSettings.ImageCropBox).ToList();
            var polygonRow = polygonResult.Where(c => c.Type == ImageConstSettings.ImageCropRow).ToList();

            // Store original polygon results
            image.OriginalBoundingBox = JsonConvert.SerializeObject(polygonBox);
            image.OriginalBoundingRows = JsonConvert.SerializeObject(polygonRow);
            
            // Set initial values same as originals
            image.BoundingBox = image.OriginalBoundingBox;
            image.BoundingRows = image.OriginalBoundingRows;

            await _repository.UpdateAsync(image);
            await _unitOfWorkManager.Current.SaveChangesAsync();
        }

        /// <summary>
        /// 
        /// </summary>
        public async Task HandleOcrStep(StepWorkflow step, ProcessImageDto input, Image image, string inputStep,
            CropCoordinate[] boundingRow)
        {
            var allOcrResults =
                await _ocrService.PerformOcrV2(null, inputStep, boundingRow);

            await SaveResultStep(step, input.ImageId, inputStep, allOcrResults);

            image.OcrResult = JsonConvert.SerializeObject(allOcrResults);
            await _repository.UpdateAsync(image);
            await _unitOfWorkManager.Current.SaveChangesAsync();
        }

        /// <summary>
        /// 
        /// </summary>
        public async Task HandlePredictDrillholeAndDepthStep(StepWorkflow step, ProcessImageDto input, Image image,
            string inputStep)
        {
            var allOcrResults = await _ocrService.PredictDrillholeAndDepth(null, inputStep);

            var predictFrom = allOcrResults.FirstOrDefault(c => c.tag == "depth_from");
            var predictTo = allOcrResults.FirstOrDefault(c => c.tag == "depth_to");
            var predictDrillHole = allOcrResults.FirstOrDefault(c => c.tag == "dh_name");

            await SaveResultStep(step, input.ImageId, inputStep, allOcrResults);

            image.DepthFromOcr = double.TryParse(predictFrom?.text, out var resultFrom) ? resultFrom : image.DepthFrom;
            image.DepthToOcr = double.TryParse(predictTo?.text, out var resultTo) ? resultTo : image.DepthTo;
            // Store original drillhole name result
            image.OriginalDrillholeNameOcr = predictDrillHole?.text;
            
            // Set initial value same as original
            image.DrillholeNameOcr = image.OriginalDrillholeNameOcr ?? image.DrillholeNameOcr;
            // image.ImageStatus = ImageStatus.Unnamed;
            await _repository.UpdateAsync(image);
            await _unitOfWorkManager.Current.SaveChangesAsync();
        }

        /// <summary>
        /// 
        /// </summary>
        public async Task HandleSegmentStep(StepWorkflow step, ProcessImageDto input, Image image, string inputStep, CropCoordinate[] boundingRow)
        {
            var segmentResult = await _segmentService.PerformSegment(null, inputStep, boundingRow,
                (BoundingRowOption)step.BoundingRowOption);
            var segmentResultConverted = SegmentResultHandler.ConvertSegmentResult(segmentResult);

            await SaveResultStep(step, input.ImageId, inputStep, segmentResultConverted);

            // Store original segment results
            image.OriginalSegmentResult = JsonConvert.SerializeObject(segmentResultConverted);
            
            // Set initial value same as original
            image.SegmentResult = image.OriginalSegmentResult;
            
            await _repository.UpdateAsync(image);
            await _unitOfWorkManager.Current.SaveChangesAsync();
        }
        
        /// <summary>
        /// 
        /// </summary>
        public async Task HandlePolygonCropOcrSegmentStep(StepWorkflow step, Image image, string inputStep, ImageStatus? imageStatusDone)
        {
            var result = await _workflowService.PerformPolygonCropOcrSegmentAsync(inputStep, image, imageStatusDone, step.Prompt, step.SegmentFlag);

            await SaveResultStep(step, image.Id, inputStep, result);

            await _unitOfWorkManager.Current.SaveChangesAsync();
        }
        
        /// <summary>
        /// 
        /// </summary>
        public async Task HandlePolygonCropOcrSegmentDetailSamStep(StepWorkflow step, Image image, string inputStep, ImageStatus? imageStatusDone)
        {
            var result = await _workflowService.PerformPolygonCropOcrSegmentDetailSamAsync(inputStep, image, imageStatusDone, step.Prompt);

            await SaveResultStep(step, image.Id, inputStep, result);

            await _unitOfWorkManager.Current.SaveChangesAsync();
        }
        
        /// <summary>
        /// 
        /// </summary>
        public async Task HandlePolygonCropOcrSegmentDetailCorePiecesStep(StepWorkflow step, Image image, string inputStep, ImageStatus? imageStatusDone)
        {
            var result = await _workflowService.PerformPolygonCropOcrSegmentDetailCorePiecesAsync(inputStep, image, imageStatusDone, step.Prompt);

            await SaveResultStep(step, image.Id, inputStep, result);

            await _unitOfWorkManager.Current.SaveChangesAsync();
        }
        
        /// <summary>
        /// 
        /// </summary>
        public async Task HandleSegmentAutoCropStepAsync(StepWorkflow step, Image image, string inputStep, ImageStatus? imageStatusDone)
        {
            var result = await _workflowService.PerformSegmentAutoCropAsync(inputStep, image, imageStatusDone);

            await SaveResultStep(step, image.Id, inputStep, result);

            await _unitOfWorkManager.Current.SaveChangesAsync();
        }
        
        /// <summary>
        /// 
        /// </summary>
        public async Task HandleSegmentationDetailStep(StepWorkflow step, Image image, string inputStep, ImageStatus? imageStatusDone)
        {
            var result = await _workflowService.PerformSegmentDetailAsync(inputStep, image, imageStatusDone);

            await SaveResultStep(step, image.Id, inputStep, result);

            await _unitOfWorkManager.Current.SaveChangesAsync();
        }
        
        /// <summary>
        /// 
        /// </summary>
        public async Task HandlePolygonCropStep(StepWorkflow step, Image image, string inputStep, ImageStatus? imageStatusDone)
        {
            var result = await _workflowService.PerformPolygonCropAsync(inputStep, image, imageStatusDone);

            await SaveResultStep(step, image.Id, inputStep, result);

            await _unitOfWorkManager.Current.SaveChangesAsync();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="step"></param>
        /// <param name="input"></param>
        /// <param name="image"></param>
        /// <param name="inputStep"></param>
        public async Task HandleDetectFractures(StepWorkflow step, ProcessImageDto input, Image image, string inputStep)
        {
            var detectResult = await _ocrService.DetectFracture(null, inputStep);
            await SaveResultStep(step, input.ImageId, inputStep, detectResult);

            // Store original fracture detection results
            image.OriginalFractures = JsonConvert.SerializeObject(detectResult);
            
            // Set initial value same as original
            image.Fractures = image.OriginalFractures;
            
            await _repository.UpdateAsync(image);
            await _unitOfWorkManager.Current.SaveChangesAsync();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="step"></param>
        /// <param name="input"></param>
        /// <param name="image"></param>
        /// <param name="inputStep"></param>
        /// <param name="cropResult"></param>
        /// <param name="boundingRow"></param>
        public async Task HandleDirectOcrStep(StepWorkflow step, ProcessImageDto input, Image image, string inputStep,
            List<ImageCrop> cropResult, CropCoordinate[] boundingRow)
        {
            var allOcrResults = new List<OcrResultV2Dto>();

            if (step.DataSourceType == DataSourceType.Step || step.DataValue == "croppedRow")
            {
                inputStep = JsonConvert.SerializeObject(cropResult);
                cropResult = cropResult.Where(crop => crop.Type == ImageConstSettings.ImageCropRow).ToList();
                foreach (var t in cropResult)
                {
                    var imageStream = await OcrService.GetImageStream(null, t.UrlCroppedImage);

                    var coordinateImgCrop =
                        JsonConvert.DeserializeObject<CropCoordinate>(t.Coordinate ?? string.Empty);

                    var ocrResult = await _ocrService.PerformOcr(imageStream, coordinateImgCrop, null);

                    if (ocrResult.Count != 0)
                    {
                        allOcrResults.AddRange(ocrResult);
                    }
                }
            }
            else
            {
                var imageStream = await OcrService.GetImageStream(null, inputStep);

                var ocrResult = await _ocrService.PerformOcr(imageStream, null, boundingRow);

                if (ocrResult.Count != 0)
                {
                    allOcrResults.AddRange(ocrResult);
                }
            }

            await SaveResultStep(step, input.ImageId, inputStep, allOcrResults);

            // Store original OCR results
            image.OriginalDirectOcrResult = JsonConvert.SerializeObject(allOcrResults);
            
            // Set initial value same as original
            image.DirectOcrResult = image.OriginalDirectOcrResult;
            
            await _repository.UpdateAsync(image);
            await _unitOfWorkManager.Current.SaveChangesAsync();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<List<ImageCrop>> HandleCropStep(StepWorkflow step, ProcessImageDto input, string inputStep,
            CropCoordinate[] cropCoordinates, int drillHoleId, bool assignStep)
        {
            var imageCrops = await _cropService.CropImg(input.ImageId, cropCoordinates, true, assignStep);
            await SaveResultStep(step, input.ImageId, inputStep, imageCrops);

            await _unitOfWorkManager.Current.SaveChangesAsync();
            await _drillHoleService.UpdateTotalImageByDrillholeAsync(drillHoleId);
            return imageCrops;
        }

        /// <summary>
        /// 
        /// </summary>
        public async Task SaveResultStep(StepWorkflow step, int imageId, string inputStep, object output)
        {
            var resultStep =
                await _resultStepRepository.FirstOrDefaultAsync(x => x.ImageId == imageId && x.StepId == step.Id);
            if (resultStep == null)
            {
                resultStep = new ResultStep
                {
                    StepId = step.Id,
                    ImageId = imageId,
                    Input = inputStep,
                    Output = JsonConvert.SerializeObject(output, new JsonSerializerSettings
                    {
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                    })
                };
                await _resultStepRepository.InsertAsync(resultStep);
            }
            else
            {
                resultStep.Input = inputStep;
                resultStep.Output = JsonConvert.SerializeObject(output, new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                });
                await _resultStepRepository.UpdateAsync(resultStep);
            }
        }
    }
}