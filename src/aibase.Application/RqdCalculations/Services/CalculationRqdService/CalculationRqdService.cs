using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.DrillHoleEntity;
using aibase.GeotechDatas;
using aibase.ImageCrops.Dto;
using aibase.ImageEntity;
using aibase.Images.Dto;
using aibase.Images.Services.UploadService.Handler;
using aibase.ImageTypes;
using aibase.RockLines;
using aibase.RqdCalculations.Dto.CalculationRqd;
using aibase.RqdCalculations.Services.CalculationRqdService.Handler;
using aibase.Structures;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace aibase.RqdCalculations.Services.CalculationRqdService;

/// <inheritdoc />
public class CalculationRqdService : ICalculationRqdService
{
    private readonly IRepository<RqdCalculationProject, int> _rqdCalculationProjectRepository;
    private readonly IRepository<RqdCalculationResult, int> _rqdCalculationResultRepository;
    private readonly IRepository<Image, int> _imageRepository;
    private readonly IRepository<DrillHole, int> _drillholeRepository;
    private readonly IRepository<GeotechData, int> _geotechDataRepository;
    private readonly IRepository<ImageType, int> _imageTypeRepository;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    public CalculationRqdService(
        IRepository<RqdCalculationProject, int> rqdCalculationProjectRepository,
        IRepository<RqdCalculationResult, int> rqdCalculationResultRepository,
        IRepository<Image, int> imageRepository,
        IRepository<DrillHole, int> drillholeRepository,
        IRepository<GeotechData, int> geotechDataRepository,
        IRepository<ImageType, int> imageTypeRepository,
        IAbpSession abpSession,
        IMapper mapper
        )
    {
        _rqdCalculationProjectRepository = rqdCalculationProjectRepository;
        _imageRepository = imageRepository;
        _drillholeRepository = drillholeRepository;
        _geotechDataRepository = geotechDataRepository;
        _abpSession = abpSession;
        _rqdCalculationResultRepository = rqdCalculationResultRepository;
        _mapper = mapper;
        _imageTypeRepository = imageTypeRepository;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<RqdCalculationResultDto>> GetRqdCalculationResultAsync(
        PagedRqdCalculationResultResultRequestDto input)
    {
        var query = _rqdCalculationResultRepository.GetAllIncluding(x => x.RqdCalculation, x => x.DrillHole)
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .Where(x => x.RqdCalculationId == input.RqdCalculationId)
            .WhereIf(input.DrillHoleId.HasValue,
                x => x.DrillHoleId == input.DrillHoleId);

        var totalCount = await query.CountAsync();

        var rqdCalculationResults = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<RqdCalculationResultDto>(totalCount,
            _mapper.Map<List<RqdCalculationResultDto>>(rqdCalculationResults));
    }

    /// <inheritdoc />
    public async Task ExecuteRqdCalculationByDrillHoleAsync(ExecuteRqdCalculationDto input)
    {
        var drillhole = await _drillholeRepository.FirstOrDefaultAsync(x => x.Id == input.DrillholeId);
        if (drillhole == null)
        {
            throw new UserFriendlyException("Drillhole not found");
        }

        var rqdCalculations = await _rqdCalculationProjectRepository.GetAll()
            .AsNoTracking()
            .Include(x => x.RqdCalculation)
            .ThenInclude(x => x.Structure)
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.RqdCalculationId.HasValue, x => x.RqdCalculationId == input.RqdCalculationId)
            .Where(x => x.ProjectId == drillhole.ProjectId)
            .Select(x => x.RqdCalculation)
            .Distinct()
            .ToListAsync();
        var rqdCalculationIds = rqdCalculations.Select(x => x.Id).ToList();

        await _rqdCalculationResultRepository.DeleteAsync(c =>
            rqdCalculationIds.Contains(c.RqdCalculationId) && c.DrillHoleId == drillhole.Id);

        var rqdStructureIds = rqdCalculations.Select(x => x.StructureId).Distinct().ToList();

        var geotechDataAll = await _geotechDataRepository.GetAll()
            .Where(x => rqdStructureIds.Contains(x.StructureId) && x.DrillHoleId == drillhole.Id)
            .ToListAsync();
        
        var images = await _imageRepository.GetAll()
            .AsNoTracking()
            .Include(x => x.ImageType)
            .Include(x => x.CroppedImages)
            .ThenInclude(x => x.RockLines)
            .Where(x => x.DrillHoleId == drillhole.Id && x.ImageType != null && x.ImageType.IsStandard)
            .Select(x => new ImageDto()
            {
                Id = x.Id,
                DepthFrom = x.DepthFrom,
                DepthTo = x.DepthTo,
                CroppedImages = _mapper.Map<List<ImageCropDto>>(x.CroppedImages),
                OcrResult = x.OcrResult
            })
            .OrderBy(x => x.DepthFrom)
            .ToListAsync();
        if (images.Count == 0)
        {
            throw new UserFriendlyException("No standard images found");
        }

        var depthFromDrillhole = images.Min(x => x.DepthFrom);
        var depthToDrillhole = images.Max(x => x.DepthTo);

        var rowsByImage = new List<List<RowSegmentRqdDto>>();
        foreach (var image in images)
        {
            // Image crop
            var imageCrops = image.CroppedImages
                .Where(x => x.Type is ImageConstSettings.ImageCropRow or ImageConstSettings.ImageCropRowLower)
                .OrderBy(x => x.DepthFrom)
                .ToList();
            // var rockLines = imageCrops.SelectMany(x => x.RockLines).ToList();
            var imageCropCoordinates = imageCrops.Select(x => x.Coordinate).ToList();
            var boundingRows = imageCropCoordinates.OfType<string>()
                .Select(JsonConvert.DeserializeObject<CoordinateBoundingDto>)
                .OfType<CoordinateBoundingDto>().ToList();
            
            if (boundingRows.Count == 0)
            {
                continue;
            }

            // OCR
            var ocrResult = JsonConvert.DeserializeObject<List<OcrResultRqdDto>>(image.OcrResult ?? "[]")
                            ?? [];

            for (var i = 0; i < imageCrops.Count; i++)
            {
                var coordinateImageCrop =
                    JsonConvert.DeserializeObject<CoordinateBoundingDto>(imageCrops[i].Coordinate ?? "") ??
                    new CoordinateBoundingDto();
                var selectedY = coordinateImageCrop.Y + coordinateImageCrop.Height / 2;

                foreach (var geotech in geotechDataAll)
                {
                    if (imageCrops[i].Id == geotech.ImageCropId)
                    {
                        var flagGeotechData = new OcrResultRqdDto
                        {
                            GeotechDataId = geotech.Id,
                            Type = "geotech",
                            X = geotech.X,
                            Y = selectedY,
                            Width = 1,
                            Height = 1,
                            RowIndex = i,
                            Text = $"{image.DepthFrom}",
                        };
                        ocrResult.Add(flagGeotechData);
                    }

                    if (geotech.ImageCropIdTo != null && imageCrops[i].Id == geotech.ImageCropIdTo)
                    {
                        var flagGeotechData = new OcrResultRqdDto
                        {
                            GeotechDataId = geotech.Id,
                            Type = "geotech",
                            X = (double)geotech.XTo,
                            Y = selectedY,
                            Width = 1,
                            Height = 1,
                            RowIndex = i,
                            Text = $"{image.DepthFrom}",
                        };
                        ocrResult.Add(flagGeotechData);
                    }
                }
            }

            var rowSegments = CalculationRqdHandler.MergeDataRowSegmentOcr(boundingRows, ocrResult);

            for (var i = 0; i < imageCrops.Count; i++)
            {
                var boundingSegments = imageCrops[i].RockLines
                    .Where(x => x.Type == RockLineType.Recovery)
                    .Select(x => new CoordinateV2Dto
                    {
                        X = Math.Min(x.StartX, x.EndX) + rowSegments[i].X,
                        Y = (rowSegments[i].Y + rowSegments[i].Height) / 2,
                        Width = Math.Abs(x.EndX - x.StartX),
                        Height = 1
                    }).ToList();
                rowSegments[i].BoundingSegment = boundingSegments;
                rowSegments[i].DepthFrom = imageCrops[i].DepthFrom ?? 0;
                rowSegments[i].DepthTo = imageCrops[i].DepthTo ?? 0;
            }
            rowsByImage.Add(rowSegments);
        }
        var horizontalRows = CalculationRqdHandler.AlignSegmentsHorizontally(rowsByImage);

        var firstRow = horizontalRows.First();
        var lastRow = horizontalRows.Last();

        var firstOcrBlock = new OcrBlockRqdDto
        {
            X = firstRow.X,
            Y = firstRow.Y,
            Width = 100,
            Height = 100,
            Type = "wooden",
            Value = firstRow.DepthFrom
        };
        var lastOcrBlock = new OcrBlockRqdDto
        {
            X = lastRow.X + lastRow.Width,
            Y = lastRow.Y,
            Width = 100,
            Height = 100,
            Type = "wooden",
            Value = lastRow.DepthTo
        };

        var horizontalOcrResults = horizontalRows
            .SelectMany(row => row.OcrResults)
            .ToList();

        var horizontalOcrBlocks = horizontalOcrResults
            .Where(item =>
                double.TryParse(item.text, NumberStyles.Any, CultureInfo.InvariantCulture, out _))
            .Select(item => new OcrBlockRqdDto
            {
                X = item.x,
                Y = item.y,
                Width = item.width,
                Height = item.height,
                Type = item.type,
                Value = double.Parse(item.text, CultureInfo.InvariantCulture),
                GeotechDataId = item.GeotechDataId
            })
            .Where(ob => ob.Value >= firstOcrBlock.Value && ob.Value <= lastOcrBlock.Value)
            .ToList();

        var fullHorizontalOcrBlocks = new List<OcrBlockRqdDto>();
        if (horizontalOcrBlocks.Count > 0 && (horizontalOcrBlocks[0].Value > firstOcrBlock.Value ||
                                              horizontalOcrBlocks[0].Type != firstOcrBlock.Type))
        {
            fullHorizontalOcrBlocks.Add(firstOcrBlock);
        }

        fullHorizontalOcrBlocks.AddRange(horizontalOcrBlocks);
        if (horizontalOcrBlocks.Count > 0 && (horizontalOcrBlocks[^1].Value < lastOcrBlock.Value ||
                                              horizontalOcrBlocks[0].Type != firstOcrBlock.Type))
        {
            fullHorizontalOcrBlocks.Add(lastOcrBlock);
        }

        foreach (var rqdCalculation in rqdCalculations)
        {
            // Rock Segments
            var boundingSegments = horizontalRows.SelectMany(x => x.BoundingSegment).ToList();

            var minimumSegmentLength = 0.0;
            if (rqdCalculation.MinimumWidth != null)
            {
                var depth = depthToDrillhole - depthFromDrillhole;
                var depthRatio = (double)rqdCalculation.MinimumWidth / depth;
                minimumSegmentLength = depthRatio * (lastRow.X - firstRow.X);
            }

            var boundingSegmentsFilter = boundingSegments.Where(x => x.Width > minimumSegmentLength).ToList();

            // Structures
            var structureCountType = rqdCalculation?.Structure?.Selector;

            var structures = fullHorizontalOcrBlocks.Where(x => x.Type == "geotech").ToList();
            var mergeStructures = CalculationRqdHandler.ProcessGeotechData(structures);
            var boundingStructures = mergeStructures.Select(x => new CoordinateV2Dto
            {
                X = x.X,
                Y = x.Y,
                Width = x.Width,
                Height = x.Height,
            }).ToList();

            if (structureCountType == StructureSelector.Points)
            {
                boundingStructures = boundingStructures.Where(x => x.Width <= 1.0).ToList();
            }
            else if (structureCountType == StructureSelector.Interval)
            {
                boundingStructures = boundingStructures.Where(x => x.Width > minimumSegmentLength).ToList();
            }

            var boundingCount = rqdCalculation.CountingMethod == CountingMethod.Structures
                ? boundingStructures
                : boundingSegmentsFilter;

            switch (rqdCalculation?.CalculationType)
            {
                case CalculationType.Block:
                {
                    var rqdBlocks = fullHorizontalOcrBlocks
                        .Where(x => x.Type is "wooden" or "Wooden")
                        .Select(x => new RqdBlockDto
                        {
                            X = x.X,
                            Depth = x.Value,
                        }).ToList();

                    await InsertRqdCalculationResults(rqdBlocks, boundingCount, drillhole.Id, rqdCalculation.Id);
                    break;
                }
                case CalculationType.Distance:
                {
                    var intervalDistance = rqdCalculation.Interval;
                    if (rqdCalculation.Interval == null)
                    {
                        throw new UserFriendlyException("Interval is required to calculate type Distance");
                    }

                    var rqdBlocks = new List<RqdBlockDto>
                    {
                        new()
                        {
                            X = firstRow.X,
                            Depth = depthFromDrillhole
                        },
                        new()
                        {
                            X = lastRow.X,
                            Depth = depthToDrillhole
                        }
                    };

                    var interpolatedPoints =
                        CalculationRqdHandler.GenerateInterpolatedPoints(rqdBlocks, (double)intervalDistance);
                    await InsertRqdCalculationResults(interpolatedPoints, boundingCount, drillhole.Id,
                        rqdCalculation.Id);

                    break;
                }
            }
        }
    }

    private async Task InsertRqdCalculationResults(List<RqdBlockDto> blocks, List<CoordinateV2Dto> rockSegments,
        int drillHoleId,
        int rqdCalculationId)
    {
        for (var i = 0; i < blocks.Count - 1; i++)
        {
            var fromDepth = blocks[i].X;
            var toDepth = blocks[i + 1].X;

            var count = rockSegments.Count(b => b.X + b.Width >= fromDepth && b.X + b.Width <= toDepth);

            var rqdCalculationResult = new RqdCalculationResult
            {
                DrillHoleId = drillHoleId,
                RqdCalculationId = rqdCalculationId,
                FromDepth = blocks[i].Depth,
                ToDepth = blocks[i + 1].Depth,
                Total = count,
                TenantId = _abpSession.GetTenantId()
            };
            await _rqdCalculationResultRepository.InsertAsync(rqdCalculationResult);
        }
    }
}