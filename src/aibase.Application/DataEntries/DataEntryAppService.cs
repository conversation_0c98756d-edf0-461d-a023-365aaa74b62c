using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using aibase.DataEntries.Dto;
using aibase.DataEntries.Services;
using Microsoft.AspNetCore.Mvc;

namespace aibase.DataEntries;

/// <inheritdoc />
public class DataEntryAppService : aibaseAppServiceBase
{
    private readonly IDataEntryService _dataEntryService;

    /// <inheritdoc />
    public DataEntryAppService(
        IDataEntryService dataEntryService)
    {
        _dataEntryService = dataEntryService;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    public async Task<DataEntry> CreateDataEntryAsync(CreateDataEntryDto input)
    {
        return await _dataEntryService.CreateDataEntryAsync(input);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<DataEntryResultDto> GetAllDataEntryAsync(PagedDataEntryRequestDto input)
    {
        return await _dataEntryService.GetAllDataEntryAsync(input);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<IActionResult> GetAllDataEntryByLoggingViewAsync(PagedDataEntryByLoggingViewRequestDto input)
    {
        return await _dataEntryService.GetDataEntryByLoggingViewAsync(input);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<IActionResult> GetAllDataEntryByDrillholeAsync(PagedDataEntryByDrillholeRequestDto input)
    {
        return await _dataEntryService.GetAllDataEntryByDrillholeAsync(input);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    public async Task UpdateDataEntryValueAsync(UpdateDataEntryValueDto input)
    {
        await _dataEntryService.UpdateDataEntryValueAsync(input);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    public async Task<DataEntry> UpdateDataEntryAsync(UpdateDataEntryDto input)
    {
        return await _dataEntryService.UpdateDataEntryAsync(input);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    public async Task UpdateDepthAsync(UpdateDepthDto input)
    {
        await _dataEntryService.UpdateDepthAsync(input);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    public async Task UpdateDepthDataEntryAsync(UpdateDepthDataEntryDto input)
    {
        await _dataEntryService.UpdateDepthDataEntryAsync(input);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    public async Task DeleteGeologyDataPoint(EntityDto<int> input)
    {
        await _dataEntryService.DeleteGeologyDataPoint(input);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    [method:HttpPost]
    public async Task DeleteDataToolAsync(DeleteDataToolDto input)
    {
        await _dataEntryService.DeleteDataToolAsync(input);
    }
}