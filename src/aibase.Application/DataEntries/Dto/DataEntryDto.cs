using System.Collections.Generic;
using Abp.Application.Services.Dto;
using Abp.AutoMapper;
using aibase.DrillHoles.Dto;

namespace aibase.DataEntries.Dto;

/// <inheritdoc />
[AutoMap(typeof(DataEntry))]
public class DataEntryDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public int GeologySuiteId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int DrillholeId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public DrillHoleDto Drillhole { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public double? DepthFrom { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public double? DepthTo { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? ImageCropId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public double? X { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public List<DataEntryValueDto> DataEntryValues { get; set; } = [];
}