using System.Collections.Generic;

namespace aibase.DataEntries.Dto;

/// <summary>
/// 
/// </summary>
public class UpdateDepthDataEntryDto
{
    /// <summary>
    /// 
    /// </summary>
    public int DrillholeId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int GeologySuiteId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public List<DataEntryUpdateDepthDto> DataEntries { get; set; } = [];
}

/// <summary>
/// 
/// </summary>
public class DataEntryUpdateDepthDto
{
    /// <summary>
    /// 
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public double DepthFrom { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public double DepthTo { get; set; }
}