using System.ComponentModel.DataAnnotations;

namespace aibase.DataEntries.Dto;

/// <summary>
/// DTO for reverse depth calculation - finding ImageCropId and X position from depth
/// </summary>
public class GetPositionByDepthDto
{
    /// <summary>
    /// The drill hole ID
    /// </summary>
    [Required]
    public int DrillHoleId { get; set; }
    
    /// <summary>
    /// The depth value to find position for
    /// </summary>
    [Required]
    public double Depth { get; set; }
}
