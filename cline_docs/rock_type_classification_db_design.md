# Rock Type Classification System Database Design

## Introduction

The Rock Type Classification System is designed to provide a flexible hierarchical categorization framework for rock types within geology suites. This document details the database design that implements this system, focusing on how it supports hierarchical classification, multiple inheritance, ordering, and association with geology suites.

The system introduces new entities to manage rock type categories and their relationships, as well as modifications to existing entities to incorporate the categorization system. The design enables geologists to organize rock types into a customizable taxonomy that can be tailored to specific geology suites.

## Entity Definitions

### RockTypeCategory

This entity represents a category in the rock type classification hierarchy. Categories can have parent-child relationships, forming a tree structure of arbitrary depth.

| Property | Data Type | Constraints | Description |
|----------|-----------|------------|-------------|
| Id | int | Primary Key | Unique identifier for the category |
| Name | string | Required | Display name of the category |
| Code | string | Required | Short code identifier for the category |
| Description | string | Nullable | Detailed description of the category |
| ParentCategoryId | int | Nullable, ForeignKey | Reference to parent category (if any) |
| Path | string | Required | Hierarchical path representation (e.g., "/1/4/16/") |
| Depth | int | Required | Depth level in the hierarchy tree (0 for root nodes) |
| IsActive | bool | Required | Indicates if the category is active |
| TenantId | int | Required | Multi-tenancy identifier |
| CreationTime | DateTime | Required | When the category was created |
| LastModificationTime | DateTime | Nullable | When the category was last modified |

**Navigation Properties:**
- `ParentCategory`: Virtual reference to the parent RockTypeCategory
- `ChildCategories`: Collection of child RockTypeCategory entities
- `RockTypeMappings`: Collection of RockTypeCategoryMapping entities linking to RockType entities

### RockTypeCategoryMapping

This entity maps rock types to categories, enabling a many-to-many relationship that supports multiple inheritance (a rock type can belong to multiple categories).

| Property | Data Type | Constraints | Description |
|----------|-----------|------------|-------------|
| Id | int | Primary Key | Unique identifier for the mapping |
| RockTypeCategoryId | int | Required, ForeignKey | Reference to the category |
| RockTypeId | int | Required, ForeignKey | Reference to the rock type |
| DisplayOrder | int | Required | Order for display within the category |
| IsDirectMember | bool | Required | Indicates direct category membership vs. inherited |
| TenantId | int | Required | Multi-tenancy identifier |

**Navigation Properties:**
- `RockTypeCategory`: Virtual reference to the associated RockTypeCategory
- `RockType`: Virtual reference to the associated RockType

### GeologySuiteRockCategory

This entity associates rock type categories with geology suites, allowing for suite-specific categorization schemes.

| Property | Data Type | Constraints | Description |
|----------|-----------|------------|-------------|
| Id | int | Primary Key | Unique identifier for the association |
| GeologySuiteId | int | Required, ForeignKey | Reference to the geology suite |
| RockTypeCategoryId | int | Required, ForeignKey | Reference to the rock type category |
| IsRootNode | bool | Required | Indicates if this is a top-level category in the suite |
| DisplayOrder | int | Required | Order for display within the suite |
| TenantId | int | Required | Multi-tenancy identifier |

**Navigation Properties:**
- `GeologySuite`: Virtual reference to the associated GeologySuite
- `RockTypeCategory`: Virtual reference to the associated RockTypeCategory

## Modified Existing Entities

### RockType

The existing RockType entity has been modified to incorporate the categorization system.

**Added Properties:**
- `CategoryMappings`: Collection of RockTypeCategoryMapping entities linking to RockTypeCategory entities

### GeologySuite

The existing GeologySuite entity has been modified to incorporate the categorization system.

**Added Properties:**
- `RockTypeCategories`: Collection of GeologySuiteRockCategory entities linking to RockTypeCategory entities

## Entity Relationships

The relationships between entities in the Rock Type Classification System are illustrated in the following diagram:

```mermaid
erDiagram
    ROCK-TYPE-CATEGORY ||--o{ ROCK-TYPE-CATEGORY : "Parent-Child"
    ROCK-TYPE-CATEGORY ||--o{ ROCK-TYPE-CATEGORY-MAPPING : "Has"
    ROCK-TYPE ||--o{ ROCK-TYPE-CATEGORY-MAPPING : "Belongs to"
    ROCK-TYPE-CATEGORY ||--o{ GEOLOGY-SUITE-ROCK-CATEGORY : "Used in"
    GEOLOGY-SUITE ||--o{ GEOLOGY-SUITE-ROCK-CATEGORY : "Contains"

    ROCK-TYPE-CATEGORY {
        int Id PK
        string Name
        string Code
        string Description
        int ParentCategoryId FK
        string Path
        int Depth
        bool IsActive
        int TenantId
        DateTime CreationTime
        DateTime LastModificationTime
    }
    
    ROCK-TYPE-CATEGORY-MAPPING {
        int Id PK
        int RockTypeCategoryId FK
        int RockTypeId FK
        int DisplayOrder
        bool IsDirectMember
        int TenantId
    }
    
    GEOLOGY-SUITE-ROCK-CATEGORY {
        int Id PK
        int GeologySuiteId FK
        int RockTypeCategoryId FK
        bool IsRootNode
        int DisplayOrder
        int TenantId
    }
    
    ROCK-TYPE {
        int Id PK
        string Name
        string Code
        string Description
        bool IsActive
        int RockStyleId FK
        int TenantId
    }
    
    GEOLOGY-SUITE {
        int Id PK
        string Name
        bool IsActive
        int TenantId
    }
```

## Design Features

The Rock Type Classification System supports the following key features through its design:

### Hierarchical Classification

The `RockTypeCategory` entity implements a hierarchical structure through:
- Self-referencing relationship via `ParentCategoryId`
- `Path` property that stores the hierarchical path (e.g., "/1/4/16/")
- `Depth` property that indicates the level in the hierarchy
- Collection of `ChildCategories` for convenient navigation

This allows for building a tree of categories with arbitrary depth, enabling taxonomic organization of rock types.

### Multiple Inheritance

The design supports multiple inheritance through:
- Many-to-many relationship between `RockTypeCategory` and `RockType` via the `RockTypeCategoryMapping` join table
- `IsDirectMember` flag in the mapping table to distinguish between direct category membership and inherited membership
- This allows a rock type to belong to multiple categories simultaneously

### Ordering

Display ordering is supported at multiple levels:
- `DisplayOrder` property in `RockTypeCategoryMapping` controls the order of rock types within a category
- `DisplayOrder` property in `GeologySuiteRockCategory` controls the order of categories within a geology suite
- This enables flexible presentation of categories and rock types in user interfaces

### Association with Geology Suites

The system associates rock type categories with geology suites through:
- `GeologySuiteRockCategory` entity that links `RockTypeCategory` to `GeologySuite`
- `IsRootNode` flag that identifies top-level categories within a suite
- This allows each geology suite to have its own customized categorization scheme

## Conclusion

The Rock Type Classification System provides a robust and flexible framework for categorizing rock types. Its design supports complex hierarchical relationships, multiple inheritance, customized ordering, and suite-specific categorization schemes. These features enable geologists to organize rock types according to their specific needs and workflows, improving data organization and retrieval within the application.