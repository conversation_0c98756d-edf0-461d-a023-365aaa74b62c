# Rock Type Classification System: Unified Folder Model Design

## 1. Overview

This design document outlines the transition from the current separate category/rock type model to a unified folder model for the rock type classification system. The new design treats all classification items as "nodes" within the same hierarchical structure, simplifying operations, enhancing flexibility, and allowing for future extensibility while maintaining all existing functionality.

## 2. Current vs. Proposed Model

### Current Model:
- **RockTypeCategory**: Represents hierarchical categories (folders)
- **RockType**: Represents actual rock types (leaf nodes)
- **RockTypeCategoryMapping**: Links rock types to categories (multiple inheritance)
- **GeologySuiteRockCategory**: Associates categories with geology suites

### Proposed Unified Model:
- **RockNode**: A unified entity representing any item in the classification hierarchy, such as categories (folders), rock types, or other future classifications. Any node can potentially act as a container for other nodes.
- **RockNodeRelation**: Links nodes to their parent nodes (multiple inheritance).
- **GeologySuiteRockNode**: Associates nodes with geology suites.

## 3. New Entity Definitions

### 3.1. RockNode

This entity represents a node in the rock type classification hierarchy. Any node can function as a container (folder) for other nodes, regardless of its specific type (e.g., a 'RockType' node can have child nodes). The system is designed to be extensible, allowing for additional node types beyond 'Category' and 'RockType' in the future.

| Property | Data Type | Constraints | Description |
|----------|-----------|------------|-------------|
| Id | int | Primary Key | Unique identifier for the node |
| Name | string | Required | Display name of the node |
| Code | string | Required | Short code identifier for the node |
| Description | string | Nullable | Detailed description of the node |
| NodeType | string | Required | Discriminator indicating the nature of the node (e.g., "Category", "RockType", "MineralizationZone"). Designed for extensibility. |
| Path | string | Required | Hierarchical path representation (e.g., "/1/4/16/") for primary path |
| Depth | int | Required | Depth level in the hierarchy tree for primary path (0 for root nodes) |
| IsActive | bool | Required | Indicates if the node is active |
| TenantId | int | Required | Multi-tenancy identifier |
| CreationTime | DateTime | Required | When the node was created |
| LastModificationTime | DateTime | Nullable | When the node was last modified |

**Navigation Properties:**
- `ParentRelations`: Collection of incoming RockNodeRelation entities
- `ChildRelations`: Collection of outgoing RockNodeRelation entities
- `GeologySuiteAssociations`: Collection of GeologySuiteRockNode entities

### 3.2. RockNodeRelation

This entity manages the relationships between nodes, enabling multiple inheritance and hierarchical structure.

| Property | Data Type | Constraints | Description |
|----------|-----------|------------|-------------|
| Id | int | Primary Key | Unique identifier for the relation |
| ParentNodeId | int | Required, ForeignKey | Reference to the parent node |
| ChildNodeId | int | Required, ForeignKey | Reference to the child node |
| IsPrimaryRelation | bool | Required | Indicates if this is the primary parent-child relationship (used for path calculation) |
| DisplayOrder | int | Required | Order for display within the parent |
| TenantId | int | Required | Multi-tenancy identifier |

**Navigation Properties:**
- `ParentNode`: Virtual reference to the parent RockNode
- `ChildNode`: Virtual reference to the child RockNode

### 3.3. GeologySuiteRockNode

This entity associates rock nodes with geology suites.

| Property | Data Type | Constraints | Description |
|----------|-----------|------------|-------------|
| Id | int | Primary Key | Unique identifier for the association |
| GeologySuiteId | int | Required, ForeignKey | Reference to the geology suite |
| RockNodeId | int | Required, ForeignKey | Reference to the rock node |
| IsRootNode | bool | Required | Indicates if this is a top-level node in the suite |
| DisplayOrder | int | Required | Order for display within the suite |
| TenantId | int | Required | Multi-tenancy identifier |

**Navigation Properties:**
- `GeologySuite`: Virtual reference to the associated GeologySuite
- `RockNode`: Virtual reference to the associated RockNode

## 4. Entity Relationships

```mermaid
erDiagram
    ROCK-NODE ||--o{ ROCK-NODE-RELATION : "Parent of"
    ROCK-NODE ||--o{ ROCK-NODE-RELATION : "Child of"
    ROCK-NODE ||--o{ GEOLOGY-SUITE-ROCK-NODE : "Used in"
    GEOLOGY-SUITE ||--o{ GEOLOGY-SUITE-ROCK-NODE : "Contains"
    %% Note: Type-specific properties like 'RockStyle' are no longer direct FKs.
    %% They can be managed via a separate NodeProperties table or as part of the NodeType-specific logic.

    ROCK-NODE {
        int Id PK
        string Name
        string Code
        string Description
        string NodeType
        string Path
        int Depth
        bool IsActive
        int TenantId
        DateTime CreationTime
        DateTime LastModificationTime
    }
    
    ROCK-NODE-RELATION {
        int Id PK
        int ParentNodeId FK
        int ChildNodeId FK
        bool IsPrimaryRelation
        int DisplayOrder
        int TenantId
    }
    
    GEOLOGY-SUITE-ROCK-NODE {
        int Id PK
        int GeologySuiteId FK
        int RockNodeId FK
        bool IsRootNode
        int DisplayOrder
        int TenantId
    }
```

## 5. Supporting the Original Requirements

### 5.1. Hierarchical Classification

The unified model maintains hierarchical classification through:
- Self-referencing relationships via `RockNodeRelation`
- `Path` property for efficient hierarchical operations
- `Depth` property to track level in hierarchy
- `IsPrimaryRelation` flag to distinguish the main path from secondary relationships

### 5.2. Multiple Inheritance

Multiple inheritance is supported through:
- Many-to-many relationship between nodes via the `RockNodeRelation` table
- A node can have multiple parent nodes
- The `IsPrimaryRelation` flag identifies the primary parent, which determines the node's path and depth

### 5.3. Ordering

Display ordering is supported at multiple levels:
- `DisplayOrder` property in `RockNodeRelation` controls the order of child nodes within a parent node
- `DisplayOrder` property in `GeologySuiteRockNode` controls the order of nodes within a geology suite

### 5.4. Association with Geology Suites

The system continues to associate nodes with geology suites through:
- `GeologySuiteRockNode` entity that links `RockNode` to `GeologySuite`
- `IsRootNode` flag that identifies top-level nodes within a suite

## 6. Migration Path

### 6.1. Database Migration

1. **Create New Tables**:
   - Create `RockNodes`, `RockNodeRelations`, and `GeologySuiteRockNodes` tables.

2. **Migrate Categories**:
   - Insert all `RockTypeCategory` records into `RockNodes` with NodeType = 'Category'.
   - Preserve IDs if possible to maintain referential integrity.

3. **Migrate Rock Types**:
   - Insert all `RockType` records into `RockNodes` with NodeType = 'RockType'.
   - Preserve IDs if possible but offset to avoid conflicts with categories.

4. **Establish Relations**:
   - For each parent-child category relationship, create a `RockNodeRelation` with `IsPrimaryRelation = true`.
   - For each `RockTypeCategoryMapping`, create a `RockNodeRelation` with `IsPrimaryRelation` based on display order or other logic.

5. **Migrate Geology Suite Associations**:
   - Transform `GeologySuiteRockCategory` records into `GeologySuiteRockNode` records.

### 6.2. API Migration Strategy

1. **Versioned APIs**:
   - Create new API endpoints for the unified model.
   - Maintain existing APIs that use the old model during a transition period.

2. **Adapter Layer**:
   - Implement adapter services that translate between old and new models.
   - This allows existing clients to continue using the old APIs while new clients can use the new unified APIs.

3. **Phased Deprecation**:
   - Phase 1: Introduce new APIs alongside existing ones.
   - Phase 2: Mark old APIs as deprecated.
   - Phase 3: Remove old APIs after clients have migrated.

## 7. Impact on Existing Implementation

### 7.1. Data Access Layer

- New repositories needed for `RockNode`, `RockNodeRelation`, and `GeologySuiteRockNode`.
- Existing repositories for `RockTypeCategory`, `RockTypeCategoryMapping`, and `GeologySuiteRockCategory` will be deprecated.

### 7.2. Application Services

- New services will be created to handle operations on the unified model.
- Existing services will either adapt to use the new model internally or be extended to support both models during transition.

### 7.3. User Interface

- UI components that display and manipulate the hierarchy need updating to work with the unified node concept.
- Tree views and other hierarchical displays can now use a single data structure rather than handling categories and rock types separately.

## 8. Code Implementation Examples

### 8.1. RockNode Entity

```csharp
// NodeType is now a string. Predefined constants can be used for known types.
public static class RockNodeTypes
{
    public const string Category = "Category";
    public const string RockType = "RockType";
    // Future types can be added here, e.g.:
    // public const string MineralizationZone = "MineralizationZone";
    // public const string AlterationType = "AlterationType";
    // public const string StructuralFeature = "StructuralFeature";
}

public class RockNode : Entity<int>, IHasCreationTime, IHasModificationTime, IMustHaveTenant
{
    public virtual string Name { get; set; }
    public virtual string Code { get; set; }
    public virtual string Description { get; set; }
    public virtual string NodeType { get; set; } // Changed from enum to string
    public virtual string Path { get; set; } // e.g., "/1/4/16/"
    public virtual int Depth { get; set; }
    public virtual bool IsActive { get; set; }
    public virtual int TenantId { get; set; }
    public virtual DateTime CreationTime { get; set; }
    public virtual DateTime? LastModificationTime { get; set; }

    public virtual ICollection<RockNodeRelation> ParentRelations { get; set; }
    public virtual ICollection<RockNodeRelation> ChildRelations { get; set; }
    public virtual ICollection<GeologySuiteRockNode> GeologySuiteAssociations { get; set; }
    
    // Helper methods might change or be handled differently based on NodeType string
    public bool IsNodeType(string typeIdentifier) => NodeType == typeIdentifier;

    // Example usage:
    // public bool IsCategory() => IsNodeType(RockNodeTypes.Category);
    // public bool IsRockType() => IsNodeType(RockNodeTypes.RockType);
}
```

## 9. Advantages of the New Model

1. **Simplified Operations**: 
   - Unified CRUD operations for both categories and rock types
   - Consistent handling of hierarchical operations
   - Single tree structure for UI components

2. **Improved Flexibility and Extensibility**:
   - Any node, regardless of its `NodeType`, can function as a folder and have child nodes. This means rock types (and any other node type) are not restricted to being leaf nodes.
   - The `NodeType` (now a string) allows for easy introduction of new node classifications beyond 'Category' and 'RockType'.
   - Properties like `RockStyleId` can be applied to various node types as needed, not strictly limited by the `NodeType`.
   - The system is designed for straightforward extension to accommodate new types of geological, structural, or project-specific classifications in the future.

3. **Reduced Code Duplication**:
   - Single set of services and repositories for node management
   - Unified tree traversal and manipulation logic

4. **Better Performance Potential**:
   - Fewer joins needed for hierarchical queries
   - More efficient caching of the unified structure

## 10. Design for Extensibility

A core design principle of the unified model is its inherent extensibility, allowing the system to adapt to future requirements and classifications without major structural changes.

### 10.1. Flexible Node Types

The `NodeType` property on the `RockNode` entity is a key enabler for extensibility. By using a string identifier (e.g., "Category", "RockType"), the system is not limited to a predefined set of types. New node types can be introduced by:

1.  **Defining a new string constant** for the `NodeType` (e.g., `RockNodeTypes.MineralizationZone` in the C# example, or simply using the string "MineralizationZone").
2.  **Updating any relevant business logic** or UI components to recognize and handle the new node type appropriately. This might involve specific behaviors, validation rules, or display characteristics associated with the new type.

This approach avoids the need for schema changes (like adding new tables for each new type) or complex enum migrations if `NodeType` were an enum.

### 10.2. Any Node as a Folder

The design explicitly allows any `RockNode` to have child nodes through the `RockNodeRelation` entity. This means:
- A `RockType` node can act as a parent to other `RockType` nodes or even nodes of different types (e.g., specific variations or sub-classifications of that rock type).
- Future node types will automatically inherit this capability to be part of the hierarchy as either a parent or a child, effectively functioning as folders if they have children.

### 10.3. Examples of Future Node Types

The system can be extended to include various other geological or project-specific classifications. Some potential future node types include:

-   **MineralizationZone**: To classify different zones of mineralization.
-   **AlterationType**: To define types of rock alteration.
-   **StructuralFeature**: To categorize faults, folds, or other geological structures.
-   **GeotechnicalDomain**: For classifying areas based on geotechnical properties.
-   **EconomicBlock**: To define blocks for resource estimation.
-   **SampleType**: To classify different types of samples.

Each of these can be seamlessly integrated into the existing `RockNode` hierarchy, leveraging the common properties and relationships.

### 10.4. Adapting Behavior for New Types

While the structure is extensible, introducing new node types will often require corresponding adaptations in the application's business logic and user interface:
-   **Specific Validations**: New types might have unique validation rules (e.g., a "MineralizationZone" might require certain associated data).
-   **Custom UI Rendering**: The UI might need to display new node types differently (e.g., with unique icons) or offer type-specific actions.
-   **Targeted Functionality**: Some operations might only be relevant for certain node types. This can be managed through conditional logic based on the `NodeType` string (e.g., `if (node.NodeType == RockNodeTypes.MineralizationZone) { /* specific logic */ }`).

### 10.5. Handling Type-Specific Properties

With the removal of direct foreign keys like `RockStyleId` from the `RockNode` entity, type-specific properties are managed in a more extensible manner. This aligns with the unified node approach where a `RockNode` is a generic container, and its specific characteristics can vary based on its `NodeType`.

There are several ways to handle these properties:

1.  **Separate `NodeProperties` Table**:
*   A dedicated table (e.g., `RockNodeProperty`) could store key-value pairs associated with a `RockNodeId`.
*   This table might look like: `(Id, RockNodeId, PropertyKey, PropertyValue, PropertyType)`.
*   This is highly flexible and allows adding any number of custom properties to any node without altering the `RockNode` schema.
*   Querying can be more complex, and `PropertyValue` might need to be a generic type or stored as a string (e.g., JSON).

2.  **JSON/JSONB Column**:
*   A `CustomProperties` (e.g., `JSONB` in PostgreSQL, `NVARCHAR(MAX)` storing JSON in SQL Server) column could be added to `RockNode`.
*   This column would store a JSON object containing all type-specific attributes.
*   Offers good flexibility and keeps properties with the node.
*   Database support for querying JSON content varies and can impact performance if not indexed properly.

3.  **Application-Level Logic with `NodeType`**:
*   For a limited set of well-defined type-specific properties, the application can infer their existence or source based on the `NodeType`.
*   For example, if `NodeType` is "RockType", the application might expect to find an associated `RockStyle` through a different mechanism or assume certain default behaviors. This is less flexible for arbitrary new properties but can be simpler for a fixed set.

4.  **Dedicated Tables for Complex Properties**:
*   If a `NodeType` has a complex set of related data (not just simple key-value pairs), it might warrant its own table linked back to `RockNode`. For example, a `MineralizationZoneDetails` table linked to `RockNode` where `NodeType` is "MineralizationZone".

The choice of approach depends on the complexity and variability of the type-specific data. For the `RockStyle` example, if it's a primary visual characteristic for `RockType` nodes, it might be managed by ensuring that `RockStyle` entities can be linked to `RockNode` entities through an intermediary table or by convention if `RockStyle` itself becomes a more abstract concept that can apply to different `NodeType`s.

The key principle is that the base `RockNode` entity remains lean and generic, while specific attributes are layered on top based on the `NodeType`, promoting extensibility.

## 11. Conclusion

The unified node model for the rock type classification system significantly enhances flexibility and extensibility while simplifying operations and maintaining existing functionalities. By treating all classification items (categories, rock types, and future additions) as generic nodes within a single hierarchy, and critically, allowing any node to function as a container for child nodes, we achieve a robust and adaptable model. This design is well-prepared for future evolution, allowing new node types (such as "MineralizationZone" or "AlterationType") to be integrated with minimal friction. The migration strategy ensures a smooth transition, preserving data integrity and supporting all original requirements for hierarchical classification, multiple inheritance, ordering, and geology suite association.