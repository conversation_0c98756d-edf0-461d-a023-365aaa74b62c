# System Patterns

## Architecture

1. Multi-layered Architecture

   - Core Layer: Domain entities, interfaces
   - Application Layer: Business logic, DTOs
   - EntityFrameworkCore Layer: Data access, migrations
   - Web.Core Layer: Web infrastructure
   - Web.Host Layer: API hosting

2. Domain-Driven Design
   - Rich domain models
   - Entity separation by bounded contexts
   - Value objects for geological data

## Key Technical Decisions

1. Data Access

   - Entity Framework Core for ORM
   - Code-first approach with migrations
   - Repository pattern implementation

2. API Design

   - RESTful API architecture
   - Token-based authentication
   - API key support for external access

3. Multi-tenancy

   - Built-in tenant isolation
   - Tenant-specific configurations
   - Shared database with tenant filtering

4. Security

   - Role-based access control
   - API key authentication
   - User project associations

5. Containerization
   - Docker support
   - Separate containers for app and UI
   - Docker Compose for orchestration

## Design Patterns Used

1. Repository Pattern
2. Unit of Work
3. Module Pattern
4. Service Layer Pattern
5. DTO Pattern
