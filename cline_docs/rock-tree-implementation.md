# Rock Tree Backend Implementation

## Table of Contents
- [System Overview](#system-overview)
  - [Purpose and Goals](#purpose-and-goals)
  - [Key Benefits](#key-benefits)
  - [Architecture Overview](#architecture-overview)
- [Database Schema](#database-schema)
  - [RockNode](#rocknode)
  - [RockNodeRelation](#rocknoderelation)
  - [RockTreeRoot](#rocktreeroot)
  - [RockTreeRootNode](#rocktreerootnode)
  - [Entity Relationships](#entity-relationships)
  - [Design Considerations](#design-considerations)
- [API Reference](#api-reference)
  - [Rock Tree Management](#rock-tree-management)
  - [Rock Node Management](#rock-node-management)
  - [Tree Traversal](#tree-traversal)
  - [Common Usage Patterns](#common-usage-patterns)
- [Service Layer](#service-layer)
  - [RockTreeService](#rocktreeservice)
  - [RockNodeService](#rocknodeservice)
  - [RockTreeTraversalService](#rocktreetraversalservice)
  - [Business Logic Details](#business-logic-details)
- [Code Examples](#code-examples)
  - [Creating a New Rock Tree](#creating-a-new-rock-tree)
  - [Building a Tree Structure](#building-a-tree-structure)
  - [Traversing the Tree](#traversing-the-tree)
  - [Optimizing Performance](#optimizing-performance)

## System Overview

### Purpose and Goals

The Rock Tree implementation represents a significant advancement in how geological rock types are organized and managed within the aibase system. This node-based approach was developed with several key goals:

1. **Flexibility in Classification**: Support for multiple classification hierarchies of rock types rather than a rigid single taxonomy
2. **Enhanced Organization**: Ability to create folders, groups, and custom organizational structures
3. **Multiple Inheritance**: Allow rock types to exist in multiple places in the hierarchy (unlike traditional tree structures)
4. **Versioning Support**: Enable different versions of classification systems to coexist
5. **Integration with GeologySuites**: Direct connection to geology suite systems for streamlined workflow

### Key Benefits

The node-based approach offers several advantages over previous implementations:

1. **Hierarchical Flexibility**: Rock types can be organized in multiple ways simultaneously
2. **Reusable Components**: Nodes can be shared across different tree structures
3. **Separation of Concerns**: Clear separation between rock type definitions and their organizational structure
4. **Intuitive Navigation**: Easy-to-navigate tree structures for end users
5. **Performance Optimization**: Optimized queries for tree traversal operations
6. **Extensibility**: Easy to extend with additional attributes and relationships
7. **Non-Destructive Editing**: Changes to one classification don't affect others

### Architecture Overview

The Rock Tree system follows a layered architecture pattern where services are automatically exposed as API endpoints:

```mermaid
graph TD
    Client[Client Applications] --> API[API Layer]
    API --> Services[Service Layer]
    Services --> Domain[Domain Model]
    Domain --> Database[(Database)]
    
    subgraph "Application Services & API Endpoints"
        RS[RockTreeService]
        NS[RockNodeService]
        TS[RockTreeTraversalService]
    end
    
    subgraph "Domain Entities"
        RN[RockNode]
        RR[RockNodeRelation]
        RT[RockTreeRoot]
        RTN[RockTreeRootNode]
    end
    
    API --> RS
    API --> NS
    API --> TS
    
    RS --> Domain
    NS --> Domain
    TS --> Domain
    
    Domain --> RN
    Domain --> RR
    Domain --> RT
    Domain --> RTN
```

**Note**: In this framework, service interfaces and implementations also serve as the API contract. The system automatically exposes service methods as API endpoints, eliminating the need for a separate controller layer.

## Database Schema

The Rock Tree system is built on four primary entities that work together to create a flexible, hierarchical structure:

### RockNode

`RockNode` is the fundamental building block of the Rock Tree system. It represents an element in the tree, which can be a folder (for organizational purposes) or a rock type (concrete or virtual).

```csharp
public class RockNode : FullAuditedEntity<int>
{
    public string Name { get; set; }
    public string? Code { get; set; }
    public string? Description { get; set; }
    public RockNodeType NodeType { get; set; }
    public int? RockTypeId { get; set; }
    public bool IsActive { get; set; }
    public string? DisplayColor { get; set; }
    public string? IconUrl { get; set; }

    // Navigation properties
    public RockType? RockType { get; set; }
    public virtual ICollection<RockNodeRelation> ParentRelations { get; set; }
    public virtual ICollection<RockNodeRelation> ChildRelations { get; set; }
    public virtual ICollection<RockTreeRootNode> RockTreeRootNodes { get; set; }
}
```

**Key Fields:**
- `Name`: The display name of the node
- `Code`: Optional code identifier (useful for integration)
- `NodeType`: Enum indicating whether this is a Folder, ConcreteRockType, or VirtualRockType
- `RockTypeId`: Reference to a RockType entity (only for ConcreteRockType or VirtualRockType)
- `IsActive`: Flag to enable/disable the node without deleting it
- `DisplayColor`: Optional hex color code for UI display
- `IconUrl`: Optional URL to an icon image

### RockNodeRelation

`RockNodeRelation` defines the parent-child relationships between nodes, creating the hierarchical structure.

```csharp
public class RockNodeRelation : CreationAuditedEntity<int>
{
    public int ParentNodeId { get; set; }
    public int ChildNodeId { get; set; }
    public int DisplayOrder { get; set; }
    public bool IsPrimary { get; set; }

    // Navigation properties
    public virtual RockNode ParentNode { get; set; }
    public virtual RockNode ChildNode { get; set; }
}
```

**Key Fields:**
- `ParentNodeId`: The ID of the parent node
- `ChildNodeId`: The ID of the child node
- `DisplayOrder`: Controls the ordering of siblings
- `IsPrimary`: Indicates if this is the primary parent-child relationship (useful for inheritance hierarchies)

### RockTreeRoot

`RockTreeRoot` represents the root level of a rock tree hierarchy.

```csharp
public class RockTreeRoot : FullAuditedEntity<int>
{
    public string Name { get; set; }
    public string? Description { get; set; }
    public int? GeologySuiteId { get; set; }
    public bool IsActive { get; set; }

    // Navigation properties
    public virtual ICollection<RockTreeRootNode> RockTreeRootNodes { get; set; }
}
```

**Key Fields:**
- `Name`: The name of the rock tree
- `Description`: Optional description of the tree's purpose or contents
- `GeologySuiteId`: Optional link to a GeologySuite
- `IsActive`: Flag to enable/disable the entire tree

### RockTreeRootNode

`RockTreeRootNode` connects top-level nodes to a tree root, defining which nodes appear at the root level of a tree.

```csharp
public class RockTreeRootNode : Entity<int>
{
    public int RockTreeRootId { get; set; }
    public int RockNodeId { get; set; }
    public int DisplayOrder { get; set; }

    // Navigation properties
    public virtual RockTreeRoot RockTreeRoot { get; set; }
    public virtual RockNode RockNode { get; set; }
}
```

**Key Fields:**
- `RockTreeRootId`: Reference to a RockTreeRoot
- `RockNodeId`: Reference to a RockNode that appears at the root level
- `DisplayOrder`: Controls the ordering of root-level siblings

### Entity Relationships

The relationships between entities form the foundation of the Rock Tree system:

```mermaid
erDiagram
    RockTreeRoot ||--o{ RockTreeRootNode : "has root nodes"
    RockNode ||--o{ RockTreeRootNode : "appears in trees"
    RockNode ||--o{ RockNodeRelation : "has children via ChildRelations"
    RockNode ||--o{ RockNodeRelation : "has parents via ParentRelations"
    RockType ||--o{ RockNode : "referenced by"
    
    RockNodeRelation {
        int id PK
        int ParentNodeId FK
        int ChildNodeId FK
        int DisplayOrder
        bool IsPrimary
    }
    
    RockTreeRootNode {
        int id PK
        int RockTreeRootId FK
        int RockNodeId FK
        int DisplayOrder
    }
    
    RockNode {
        int id PK
        string Name
        string Code
        string Description
        RockNodeType NodeType
        int RockTypeId FK
        bool IsActive
        string DisplayColor
        string IconUrl
    }
    
    RockTreeRoot {
        int id PK
        string Name
        string Description
        int GeologySuiteId FK
        bool IsActive
    }
```

**Key Relationships:**

1. **Many-to-Many Node Relationships**:
   - A RockNode can have multiple parents and multiple children, creating a graph structure rather than a strict tree
   - This is implemented through the ParentRelations and ChildRelations navigation properties in RockNode
   - RockNodeRelation serves as a join entity connecting parent and child nodes with additional metadata

2. **Root Node Connections**:
   - A RockNode can appear at the root level of multiple trees
   - RockTreeRootNode connects RockNodes to RockTreeRoots with display ordering information
   - This allows the same node to be a root-level element in different classification systems

3. **Rock Type Association**:
   - A RockNode can reference a RockType entity via RockTypeId
   - This links the organizational structure to actual rock type definitions
   - The NodeType enum (Folder, ConcreteRockType, VirtualRockType) determines how the RockNode relates to RockType

4. **GeologySuite Integration**:
   - A RockTreeRoot can be associated with a GeologySuite via GeologySuiteId
   - This allows geology suites to have their own specialized rock classification systems
   - The relationship is optional (GeologySuiteId is nullable)

**Relationship Cardinality:**

1. **RockTreeRoot to RockTreeRootNode**: One-to-Many
   - A RockTreeRoot can have multiple RockTreeRootNodes
   - Each RockTreeRootNode belongs to exactly one RockTreeRoot

2. **RockNode to RockTreeRootNode**: One-to-Many
   - A RockNode can be associated with multiple RockTreeRootNodes
   - Each RockTreeRootNode references exactly one RockNode

3. **RockNode to RockNodeRelation (as Parent)**: One-to-Many
   - A RockNode can be the parent in multiple RockNodeRelations
   - Each RockNodeRelation has exactly one parent RockNode

4. **RockNode to RockNodeRelation (as Child)**: One-to-Many
   - A RockNode can be the child in multiple RockNodeRelations
   - Each RockNodeRelation has exactly one child RockNode

5. **RockType to RockNode**: One-to-Many
   - A RockType can be referenced by multiple RockNodes
   - A RockNode can reference at most one RockType (nullable relationship)

**Multiple Inheritance Implementation:**

The Rock Tree system implements multiple inheritance through its relation structure:

1. A RockNode can have multiple parent nodes via the ParentRelations collection
2. The IsPrimary flag in RockNodeRelation indicates which parent-child relationship is considered primary
3. This allows a rock type to inherit characteristics from multiple classifications while maintaining a primary lineage
4. When traversing the tree, applications can choose to follow all parent paths or only the primary path

**Important Constraints and Validations:**

1. **Circular References**: The system must prevent circular references where a node becomes its own ancestor
2. **Orphaned Nodes**: Nodes should always have at least one path to a root (unless they are root nodes themselves)
3. **Active Status**: The IsActive flag on both RockNode and RockTreeRoot controls visibility
4. **Display Order**: Both RockNodeRelation and RockTreeRootNode include DisplayOrder fields to control presentation

### Design Considerations

Several important considerations influenced the database schema design:

1. **Scalability**: The use of integer primary keys provides excellent performance while maintaining scalability
2. **Flexibility**: The separation of nodes and relations allows complex hierarchical structures
3. **Audit Trail**: Inheriting from `FullAuditedEntity` provides built-in tracking of creation, modifications, and soft deletion
4. **Performance**: Carefully designed indexes on foreign keys and frequently queried fields
5. **Data Integrity**: Foreign key constraints ensure referential integrity
6. **Multiple Tree Support**: The design allows multiple hierarchies to exist side-by-side

## API Reference

The Rock Tree API is accessed through service interfaces that are automatically exposed as API endpoints by the framework. There is no need to implement separate controllers.

### Rock Tree Management

**Service:** `IRockTreeService`
**API Endpoint Base:** `/api/services/app/RockTree/[action]`

| Method | API Endpoint | Description | Parameters | Returns |
|--------|--------------|-------------|------------|---------|
| `CreateAsync` | POST /Create | Creates a new RockTreeRoot | `CreateRockTreeRootInput` | `RockTreeRootDto` |
| `GetAsync` | GET /Get | Gets a RockTreeRoot by ID | `id: int` | `RockTreeRootDto` |
| `GetAllAsync` | GET /GetAll | Gets a paged list of RockTreeRoots | `PagedRockTreeRootResultRequestDto` | `PagedResultDto<RockTreeRootDto>` |
| `UpdateAsync` | PUT /Update | Updates a RockTreeRoot | `UpdateRockTreeRootInput` | `RockTreeRootDto` |
| `DeleteAsync` | DELETE /Delete | Deletes a RockTreeRoot | `id: int` | void |
| `AssociateGeologySuiteAsync` | POST /AssociateGeologySuite | Associates a RockTreeRoot with a GeologySuite | `AssociateGeologySuiteInput` | void |
| `DisassociateGeologySuiteAsync` | POST /DisassociateGeologySuite | Disassociates a RockTreeRoot from a GeologySuite | `DisassociateGeologySuiteInput` | void |

**Example Request/Response:**

Create a new Rock Tree Root:

```json
// POST /api/services/app/RockTree/Create
// Request
{
  "name": "Standard Rock Classification",
  "description": "Classification system based on IUGS standard",
  "geologySuiteId": 42,
  "isActive": true
}

// Response
{
  "id": 42,
  "name": "Standard Rock Classification",
  "description": "Classification system based on IUGS standard",
  "geologySuiteId": 42,
  "isActive": true,
  "creationTime": "2025-05-20T12:00:00.000Z",
  "creatorUserId": 1,
  "lastModificationTime": "2025-05-20T12:00:00.000Z",
  "lastModifierUserId": 1
}
```

### Rock Node Management

**Service:** `IRockNodeService`
**API Endpoint Base:** `/api/services/app/RockNode/[action]`

| Method | API Endpoint | Description | Parameters | Returns |
|--------|--------------|-------------|------------|---------|
| `CreateAsync` | POST /Create | Creates a generic RockNode | `CreateRockNodeInput` | `RockNodeDto` |
| `GetAsync` | GET /Get | Gets a RockNode by ID | `id: int` | `RockNodeDto` |
| `GetAllAsync` | GET /GetAll | Gets a paged list of RockNodes | `PagedRockNodeResultRequestDto` | `PagedResultDto<RockNodeDto>` |
| `UpdateAsync` | PUT /Update | Updates a RockNode | `UpdateRockNodeInput` | `RockNodeDto` |
| `DeleteAsync` | DELETE /Delete | Deletes a RockNode | `id: int` | void |
| `CreateFolderNodeAsync` | POST /CreateFolder | Creates a folder node | `CreateFolderNodeInput` | `RockNodeDto` |
| `CreateRockTypeNodeAsync` | POST /CreateRockType | Creates a rock type node | `CreateRockTypeNodeInput` | `RockNodeDto` |
| `CloneNodeAsync` | POST /Clone | Clones a node and its descendants | `CloneNodeInput` | `RockNodeDto` |
| `MoveNodeAsync` | POST /Move | Moves a node to a new parent | `MoveNodeInput` | `RockNodeDto` |
| `ReorderSiblingNodesAsync` | POST /ReorderSiblings | Reorders sibling nodes | `ReorderSiblingsInput` | void |
| `ActivateNodeAsync` | POST /Activate | Activates a node | `nodeId: int` | void |
| `DeactivateNodeAsync` | POST /Deactivate | Deactivates a node | `nodeId: int` | void |

**Example Request/Response:**

Create a new folder node:

```json
// POST /api/services/app/RockNode/CreateFolder
// Request
{
  "name": "Igneous Rocks",
  "description": "Rocks formed through the cooling and solidification of magma or lava",
  "parentNodeId": "3a6b7e5c-9d8f-4a2b-8c7e-6d5f4a3b2c1d",
  "rockTreeRootId": "f58e7f0d-8207-4b01-8bcf-a9c29a63a8f2",
  "displayColor": "#e67e22",
  "iconUrl": "https://assets.aibase.com/icons/folder.png",
  "order": 1
}

// Response
{
  "id": 101,
  "name": "Igneous Rocks",
  "code": null,
  "description": "Rocks formed through the cooling and solidification of magma or lava",
  "nodeType": 0,
  "rockTypeId": null,
  "isActive": true,
  "displayColor": "#e67e22",
  "iconUrl": "https://assets.aibase.com/icons/folder.png",
  "rockType": null,
  "creationTime": "2025-05-20T12:05:00.000Z",
  "creatorUserId": 1,
  "lastModificationTime": "2025-05-20T12:05:00.000Z",
  "lastModifierUserId": 1
}
```

### Tree Traversal

**Service:** `IRockTreeTraversalService`
**API Endpoint Base:** `/api/services/app/RockTreeTraversal/[action]`

| Method | API Endpoint | Description | Parameters | Returns |
|--------|--------------|-------------|------------|---------|
| `GetDescendantsAsync` | GET /GetDescendants | Gets all descendants of a node | `nodeId: int, includeInactive: bool = false` | `List<RockNodeDto>` |
| `GetAncestorsAsync` | GET /GetAncestors | Gets all ancestors of a node | `nodeId: int, includeInactive: bool = false` | `List<RockNodeDto>` |
| `GetPathAsync` | GET /GetPath | Gets the path to a node | `nodeId: int, rockTreeRootId: int? = null` | `RockNodePathDto` |
| `GetChildrenAsync` | GET /GetChildren | Gets direct children of a node | `nodeId: int, includeInactive: bool = false` | `List<RockNodeDto>` |
| `GetNodesByCategoryIdAsync` | GET /GetNodesByCategory | Gets nodes by rock type category | `categoryId: int, rockTreeRootId: int? = null, includeInactive: bool = false` | `List<RockNodeDto>` |
| `GetNodesByRockTypeIdAsync` | GET /GetNodesByRockType | Gets nodes by rock type | `rockTypeId: int, rockTreeRootId: int? = null, includeInactive: bool = false` | `List<RockNodeDto>` |

**Example Request/Response:**

Get the ancestors of a node:

```json
// GET /api/services/app/RockTreeTraversal/GetAncestors?nodeId=101
// Response
[
  {
    "id": 42,
    "name": "Rock Classification",
    "code": "RC-001",
    "description": "Root level classification",
    "nodeType": 0,
    "rockTypeId": null,
    "isActive": true,
    "displayColor": "#3498db",
    "iconUrl": "https://assets.aibase.com/icons/root.png",
    "rockType": null,
    "creationTime": "2025-05-20T12:00:00.000Z",
    "creatorUserId": 1,
    "lastModificationTime": "2025-05-20T12:00:00.000Z",
    "lastModifierUserId": 1
  }
]
```

### Common Usage Patterns

1. **Creating a New Classification System**:
   - Create a `RockTreeRoot`
   - Create folder nodes at the root level
   - Build the hierarchy by adding child nodes
   - Associate with a GeologySuite if needed

2. **Navigating the Tree**:
   - Get root level nodes for a particular tree
   - Traverse down using `GetChildren` or `GetDescendants`
   - Navigate up using `GetAncestors` or `GetPath`

3. **Rock Type Management**:
   - Create rock type nodes connected to actual `RockType` entities
   - Allow the same rock type to appear in multiple places in the hierarchy
   - Implement custom views based on rock type categories

4. **Tree Maintenance**:
   - Reorganize using `Move` and `ReorderSiblings`
   - Clone subtrees for creating similar structures
   - Activate/deactivate nodes as needed

## Service Layer

The service layer implements the business logic for managing and traversing rock trees.

### RockTreeService

**Interface:** `IRockTreeService`

```csharp
public interface IRockTreeService : IAsyncCrudAppService<RockTreeRootDto, int, PagedRockTreeRootResultRequestDto, CreateRockTreeRootInput, UpdateRockTreeRootInput>
{
    Task AssociateGeologySuiteAsync(AssociateGeologySuiteInput input);
    Task DisassociateGeologySuiteAsync(DisassociateGeologySuiteInput input);
}
```

The `RockTreeService` handles operations related to rock tree roots:

- **CRUD Operations**: Standard create, read, update, delete operations for rock tree roots
- **GeologySuite Association**: Connecting and disconnecting rock trees from geology suites
- **Root Node Management**: Managing which nodes appear at the root level of the tree

### RockNodeService

**Interface:** `IRockNodeService`

```csharp
public interface IRockNodeService : IAsyncCrudAppService<RockNodeDto, int, PagedRockNodeResultRequestDto, CreateRockNodeInput, UpdateRockNodeInput>
{
    Task<RockNodeDto> CreateFolderNodeAsync(CreateFolderNodeInput input);
    Task<RockNodeDto> CreateRockTypeNodeAsync(CreateRockTypeNodeInput input);
    Task<RockNodeDto> CloneNodeAsync(CloneNodeInput input);
    Task<RockNodeDto> MoveNodeAsync(MoveNodeInput input);
    Task ReorderSiblingNodesAsync(ReorderSiblingsInput input);
    Task ActivateNodeAsync(int nodeId);
    Task DeactivateNodeAsync(int nodeId);
}
```

The `RockNodeService` is responsible for:

- **Node Creation**: Creating different types of nodes (folders, rock types)
- **Structural Operations**: Cloning, moving, and reordering nodes
- **State Management**: Activating and deactivating nodes

### RockTreeTraversalService

**Interface:** `IRockTreeTraversalService`

```csharp
public interface IRockTreeTraversalService : IApplicationService
{
    Task<List<RockNodeDto>> GetDescendantsAsync(int nodeId, bool includeInactive = false);
    Task<List<RockNodeDto>> GetAncestorsAsync(int nodeId, bool includeInactive = false);
    Task<RockNodePathDto> GetPathAsync(int nodeId, int? rockTreeRootId = null);
    Task<List<RockNodeDto>> GetChildrenAsync(int nodeId, bool includeInactive = false);
    Task<List<RockNodeDto>> GetNodesByCategoryIdAsync(int categoryId, int? rockTreeRootId = null, bool includeInactive = false);
    Task<List<RockNodeDto>> GetNodesByRockTypeIdAsync(int rockTypeId, int? rockTreeRootId = null, bool includeInactive = false);
}
```

The `RockTreeTraversalService` specializes in navigational operations:

- **Hierarchical Traversal**: Getting ancestors, descendants, children, and paths
- **Category-Based Queries**: Finding nodes by rock type category
- **Rock Type Queries**: Finding nodes associated with specific rock types

### Business Logic Details

The services implement important business rules and optimizations:

1. **Tree Integrity Rules**:
   - Preventing circular references (a node cannot be its own ancestor)
   - Enforcing parent-child relationship constraints
   - Managing order values for proper sibling display

2. **Transactional Operations**:
   - Complex operations like cloning and moving nodes are wrapped in transactions
   - If any part of the operation fails, the entire operation is rolled back

3. **Cascading Actions**:
   - When deactivating a node, consider implications for descendant nodes
   - When deleting a node, properly remove all associated relations

4. **Performance Optimizations**:
   - Query optimization for traversal operations
   - Efficient algorithms for deep cloning
   - Proper indexing for relation-based queries

## Code Examples

### Creating a New Rock Tree

This example shows how to create a new rock classification hierarchy using service methods that are also available as API endpoints:

```csharp
// 1. Create a new rock tree root (accessible via API at POST /api/services/app/RockTree/Create)
var createTreeInput = new CreateRockTreeRootInput
{
    Name = "Igneous Classification",
    Description = "Classification system for igneous rocks",
    IsActive = true
};

var rockTree = await _rockTreeService.CreateAsync(createTreeInput);

// 2. Create root level folders (accessible via API at POST /api/services/app/RockNode/CreateFolder)
var createFolderInput = new CreateFolderNodeInput
{
    Name = "Plutonic",
    Description = "Intrusive igneous rocks",
    RockTreeRootId = rockTree.Id,
    IsActive = true
};

var plutonicFolder = await _rockNodeService.CreateFolderNodeAsync(createFolderInput);

// 3. Add a rock type under a folder (accessible via API at POST /api/services/app/RockNode/CreateRockType)
var createRockTypeInput = new CreateRockTypeNodeInput
{
    Name = "Granite",
    Code = "IGN-GR",
    Description = "Coarse-grained intrusive igneous rock",
    RockTypeId = 101, // ID of the granite rock type
    ParentNodeId = plutonicFolder.Id,
    RockTreeRootId = rockTree.Id,
    DisplayColor = "#fd7e14",
    IsActive = true
};

await _rockNodeService.CreateRockTypeNodeAsync(createRockTypeInput);

// Note: All service methods above are automatically exposed as API endpoints
```

### Building a Tree Structure

How to build a more complex tree structure with reused nodes:

```csharp
// Create a subtree
async Task<List<Guid>> BuildSubtree(Guid rockTreeRootId, Guid parentNodeId, string prefix, int depth, int breadth)
{
    var nodeIds = new List<Guid>();
    
    if (depth <= 0) return nodeIds;
    
    for (int i = 1; i <= breadth; i++)
    {
        var folderInput = new CreateFolderNodeInput
        {
            Name = $"{prefix} {i}",
            Description = $"Folder at depth {depth}, item {i}",
            ParentNodeId = parentNodeId,
            RockTreeRootId = rockTreeRootId
        };
        
        var folder = await _rockNodeService.CreateFolderNodeAsync(folderInput);
        nodeIds.Add(folder.Id);
        
        // Recursively build children
        await BuildSubtree(rockTreeRootId, folder.Id, $"{prefix}.{i}", depth - 1, breadth);
    }
    
    return nodeIds;
}

// Usage: Build a 3-level deep tree with 3 nodes at each level
var rootNodeIds = await BuildSubtree(treeRootId, parentId, "Category", 3, 3);

// Create a shared node that appears in multiple places
var sharedNodeInput = new CreateRockTypeNodeInput
{
    Name = "Shared Rock Type",
    RockTypeId = 42,
    RockTreeRootId = treeRootId
};

var sharedNode = await _rockNodeService.CreateRockTypeNodeAsync(sharedNodeInput);

// Link the shared node to multiple parents
foreach (var rootNodeId in rootNodeIds)
{
    var moveInput = new MoveNodeInput
    {
        NodeIdToMove = sharedNode.Id,
        NewParentNodeId = rootNodeId
    };
    
    await _rockNodeService.MoveNodeAsync(moveInput);
}
```

### Traversing the Tree

Methods for traversing and inspecting the tree structure (all automatically exposed as API endpoints):

```csharp
// Get the full path to a node (accessible via API at GET /api/services/app/RockTreeTraversal/GetPath)
async Task<string> GetNodePath(int nodeId)
{
    // This service method is automatically exposed as an API endpoint
    var path = await _rockTreeTraversalService.GetPathAsync(nodeId);
    
    var pathStr = string.Join(" > ", path.Path.Select(n => n.Name));
    return $"{pathStr} > {path.TargetNodeName}";
}

// Find all leaf nodes in a tree
// (Uses GetDescendantsAsync method accessible via API at GET /api/services/app/RockTreeTraversal/GetDescendants)
async Task<List<RockNodeDto>> GetAllLeafNodes(int rootNodeId)
{
    var allNodes = await _rockTreeTraversalService.GetDescendantsAsync(rootNodeId, includeInactive: false);
    var allNodeIds = allNodes.Select(n => n.Id).ToHashSet();
    
    // A leaf node is one that doesn't appear as a parent in any relation
    return allNodes.Where(node => !allNodes.Any(n =>
        allNodes.Any(descendant =>
            descendant.Id != node.Id &&
            // This is where we'd check if node is a parent of descendant
            // In real code, we'd query RockNodeRelations
            descendant.ParentNodeId == node.Id)
    )).ToList();
}

// Get all instances of a rock type across the tree
// (accessible via API at GET /api/services/app/RockTreeTraversal/GetNodesByRockType)
async Task<List<RockNodeDto>> GetAllInstancesOfRockType(int rockTypeId)
{
    return await _rockTreeTraversalService.GetNodesByRockTypeIdAsync(
        rockTypeId,
        rockTreeRootId: null,  // Search all trees
        includeInactive: false
    );
}
```

### Optimizing Performance

Strategies for optimizing performance with large rock trees:

```csharp
// Efficient batch loading of a subtree
async Task<Dictionary<int, List<RockNodeDto>>> LoadTreeEfficiently(int rootNodeId)
{
    // This approach loads all descendants at once rather than making separate calls for each level
    var allDescendants = await _rockTreeTraversalService.GetDescendantsAsync(rootNodeId);
    
    // Group by parent ID for efficient traversal
    var nodesByParentId = new Dictionary<int, List<RockNodeDto>>();
    
    foreach (var node in allDescendants)
    {
        // In real code, we'd get the parent ID from relations
        // This is pseudocode to illustrate the concept
        if (node.ParentNodeId.HasValue)
        {
            if (!nodesByParentId.ContainsKey(node.ParentNodeId.Value))
            {
                nodesByParentId[node.ParentNodeId.Value] = new List<RockNodeDto>();
            }
            
            nodesByParentId[node.ParentNodeId.Value].Add(node);
        }
    }
    
    return nodesByParentId;
}

// Use caching for frequently accessed subtrees
private readonly IMemoryCache _cache;

async Task<List<RockNodeDto>> GetCachedSubtree(int nodeId)
{
    string cacheKey = $"RockNode_Subtree_{nodeId}";
    
    if (!_cache.TryGetValue(cacheKey, out List<RockNodeDto> subtree))
    {
        // Cache miss, load from database
        subtree = await _rockTreeTraversalService.GetDescendantsAsync(nodeId);
        
        // Cache with expiration
        var cacheOptions = new MemoryCacheEntryOptions()
            .SetSlidingExpiration(TimeSpan.FromMinutes(5))
            .SetAbsoluteExpiration(TimeSpan.FromHours(1));
            
        _cache.Set(cacheKey, subtree, cacheOptions);
    }
    
    return subtree;
}

// Invalidate cache when nodes change
async Task UpdateNodeAndInvalidateCache(UpdateRockNodeInput input)
{
    await _rockNodeService.UpdateAsync(input);
    
    // Invalidate cache for this node and its ancestors
    string cacheKey = $"RockNode_Subtree_{input.Id}";
    _cache.Remove(cacheKey);
    
    // Also invalidate cache for ancestors
    var ancestors = await _rockTreeTraversalService.GetAncestorsAsync(input.Id);
    foreach (var ancestor in ancestors)
    {
        _cache.Remove($"RockNode_Subtree_{ancestor.Id}");
    }
}

## ID Type Rationale

### Migration from Guid to Integer IDs

In the initial design of the Rock Tree system, `Guid` was chosen as the primary key type for the entities to support distributed systems and high-volume data scenarios. However, after further evaluation and practical implementation experience, the decision was made to switch to `int` type IDs for the following reasons:

1. **Performance Optimization**: Integer primary keys offer significantly better performance for:
   - Indexing and searching
   - Join operations between tables
   - Reduced storage requirements (4 bytes vs 16 bytes)
   - Faster query execution times

2. **Consistency with Existing Entities**: Most other entities in the aibase system already used integer IDs, so this change improves consistency across the codebase.

3. **Simplified Integration**: Integration with existing systems and reporting tools is more straightforward with integer IDs.

4. **Database Efficiency**: Integer keys typically result in smaller index structures and better cache utilization in database engines.

5. **Predictable Sequence**: Unlike GUIDs, integers provide a predictable, sequential order which is beneficial for certain operations and visible sorting in the UI.

The migration from `Guid` to `int` involved careful refactoring of the domain entities, repositories, and service interfaces. Database migration scripts were created to preserve data relationships during the transition. All API endpoints were updated to accept integer parameters instead of `Guid` strings.

This change does not affect the core functionality or flexible design of the Rock Tree system, but it does provide tangible benefits in terms of performance and system integration.