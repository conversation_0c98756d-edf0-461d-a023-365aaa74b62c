Introduction
The Rock Tree API provides endpoints to manage hierarchical rock classification trees. This API supports creating, reading, updating, and deleting rock tree nodes, as well as specialized operations like moving nodes, reordering siblings, and traversing the tree.

Key Features
Unified node-based approach for both folders and rock types
Support for multiple inheritance (nodes can have multiple parents)
Tree traversal capabilities (ancestors, descendants, paths)
Node manipulation operations (move, clone, reorder)
Multi-tenancy support
Multi-Tenancy Note
All rock tree resources are tenant-specific. The current tenant is automatically determined from the user's session (AbpSession). All requests are filtered by tenant without requiring explicit tenant parameters. Attempts to access resources from another tenant will result in authorization errors.

API URL Pattern
All endpoints follow this pattern:

/api/services/app/{ServiceName}/{MethodName}

txt


Node Management
Get Node
HTTP Method: GET
Endpoint: /api/services/app/RockNodeService/Get
Description: Retrieves a specific rock node by ID.
Parameters:
id (int): ID of the rock node
Response: RockNodeDto object
Get All Nodes
HTTP Method: GET
Endpoint: /api/services/app/RockNodeService/GetAll
Description: Retrieves all rock nodes with optional filtering.
Parameters:
Keyword (string, optional): Filter by name or description
IsActive (boolean, optional): Filter by active status
NodeType (int, optional): Filter by node type
RockTypeId (int, optional): Filter by rock type
ParentNodeId (int, optional): Filter by parent node
RockTreeRootId (int, optional): Filter by tree root
Response: List of RockNodeDto objects
Create Node
HTTP Method: POST
Endpoint: /api/services/app/RockNodeService/Create
Description: Creates a new rock node.
Request Body:
{
  "name": "Basalt",
  "code": "BST",
  "description": "Fine-grained, dark-colored igneous rock",
  "nodeType": 1,
  "rockTypeId": 123,
  "isActive": true,
  "displayColor": "#2A3B4C",
  "iconUrl": null,  // Frontend will determine icon based on rockType
  "parentNodeId": 10,
  "rockTreeRootId": 1,
  "order": 3
}

json


Response: Created RockNodeDto object
Create Folder Node
HTTP Method: POST
Endpoint: /api/services/app/RockNodeService/CreateFolderNode
Description: Creates a new folder node.
Request Body:
{
  "name": "Igneous Rocks",
  "code": "IGN",
  "description": "Rocks formed through cooling and solidification of magma or lava",
  "isActive": true,
  "displayColor": "#3B5998",
  "iconUrl": null,  // Frontend will determine folder icon
  "parentNodeId": 5,
  "rockTreeRootId": 1,
  "order": 1
}

json


Response: Created RockNodeDto object
Create Rock Type Node
HTTP Method: POST
Endpoint: /api/services/app/RockNodeService/CreateRockTypeNode
Description: Creates a new rock type node.
Request Body:
{
  "name": "Granite",
  "code": "GRN",
  "description": "Coarse-grained igneous rock",
  "rockTypeId": 456,
  "isActive": true,
  "displayColor": "#FFA500",
  "iconUrl": null,  // Frontend will determine icon based on rockType
  "parentNodeId": 10,
  "rockTreeRootId": 1,
  "order": 2
}

json


Response: Created RockNodeDto object
Update Node
HTTP Method: PUT
Endpoint: /api/services/app/RockNodeService/Update
Description: Updates an existing rock node.
Request Body:
{
  "id": 15,
  "name": "Updated Granite",
  "code": "GRN-2",
  "description": "Updated description",
  "isActive": true,
  "displayColor": "#228B22",
  "iconUrl": null  // Frontend will determine icon based on rockType
}

json


Response: Updated RockNodeDto object
Delete Node
HTTP Method: DELETE
Endpoint: /api/services/app/RockNodeService/Delete
Description: Deletes a rock node.
Parameters:
id (int): ID of the rock node to delete
Response: None
Clone Node
HTTP Method: POST
Endpoint: /api/services/app/RockNodeService/CloneNode
Description: Creates a copy of a node with its descendants.
Request Body:
{
  "nodeIdToClone": 10,
  "newName": "Copy of Igneous Rocks",
  "newParentNodeId": 5,
  "targetRockTreeRootId": 1,
  "includeDescendants": true
}

json


Response: Cloned RockNodeDto object
Move Node
HTTP Method: POST
Endpoint: /api/services/app/RockNodeService/MoveNode
Description: Moves a node to a new parent.
Request Body:
{
  "nodeIdToMove": 15,
  "newParentNodeId": 20,
  "newOrder": 3
}

json


Response: Moved RockNodeDto object
Reorder Sibling Nodes
HTTP Method: POST
Endpoint: /api/services/app/RockNodeService/ReorderSiblingNodes
Description: Changes the display order of nodes under the same parent.
Request Body:
{
  "parentNodeId": 10,
  "orderedNodeIds": [12, 15, 18, 20]
}

json


Response: None
Activate Node
HTTP Method: POST
Endpoint: /api/services/app/RockNodeService/ActivateNode
Description: Activates a deactivated node.
Parameters:
nodeId (int): ID of the node to activate
Response: None
Deactivate Node
HTTP Method: POST
Endpoint: /api/services/app/RockNodeService/DeactivateNode
Description: Deactivates a node.
Parameters:
nodeId (int): ID of the node to deactivate
Response: None
Tree Management
Get Rock Tree Root
HTTP Method: GET
Endpoint: /api/services/app/RockTreeService/Get
Description: Retrieves a specific rock tree root by ID.
Parameters:
id (int): ID of the rock tree root
Response: RockTreeRootDto object
Get All Rock Tree Roots
HTTP Method: GET
Endpoint: /api/services/app/RockTreeService/GetAll
Description: Retrieves all rock tree roots with optional filtering.
Parameters:
Keyword (string, optional): Filter by name or description
IsActive (boolean, optional): Filter by active status
GeologySuiteId (int, optional): Filter by geology suite
Response: List of RockTreeRootDto objects
Create Rock Tree Root
HTTP Method: POST
Endpoint: /api/services/app/RockTreeService/Create
Description: Creates a new rock tree root.
Request Body:
{
  "name": "Igneous Rocks Classification",
  "description": "Classification system for igneous rocks",
  "isActive": true,
  "geologySuiteId": 42
}

json


Response: Created RockTreeRootDto object
Update Rock Tree Root
HTTP Method: PUT
Endpoint: /api/services/app/RockTreeService/Update
Description: Updates an existing rock tree root.
Request Body:
{
  "id": 1,
  "name": "Updated Igneous Rocks Classification",
  "description": "Updated classification system",
  "isActive": true
}

json


Response: Updated RockTreeRootDto object
Delete Rock Tree Root
HTTP Method: DELETE
Endpoint: /api/services/app/RockTreeService/Delete
Description: Deletes a rock tree root.
Parameters:
id (int): ID of the rock tree root to delete
Response: None
Associate with Geology Suite
HTTP Method: POST
Endpoint: /api/services/app/RockTreeService/AssociateGeologySuite
Description: Associates a rock tree root with a geology suite.
Request Body:
{
  "rockTreeRootId": 1,
  "geologySuiteId": 42
}

json


Response: None
Disassociate from Geology Suite
HTTP Method: POST
Endpoint: /api/services/app/RockTreeService/DisassociateGeologySuite
Description: Removes association between a rock tree root and a geology suite.
Request Body:
{
  "rockTreeRootId": 1
}

json


Response: None
Tree Traversal
Get Children
HTTP Method: GET
Endpoint: /api/services/app/RockTreeTraversalService/GetChildren
Description: Gets direct children of a node.
Parameters:
nodeId (int): ID of the parent node
includeInactive (boolean, optional): Include inactive nodes
Response: List of RockNodeDto objects
Get Descendants
HTTP Method: GET
Endpoint: /api/services/app/RockTreeTraversalService/GetDescendants
Description: Gets all descendants of a node.
Parameters:
nodeId (int): ID of the ancestor node
includeInactive (boolean, optional): Include inactive nodes
Response: List of RockNodeDto objects
Get Ancestors
HTTP Method: GET
Endpoint: /api/services/app/RockTreeTraversalService/GetAncestors
Description: Gets all ancestors of a node.
Parameters:
nodeId (int): ID of the descendant node
includeInactive (boolean, optional): Include inactive nodes
Response: List of RockNodeDto objects
Get Path
HTTP Method: GET
Endpoint: /api/services/app/RockTreeTraversalService/GetPath
Description: Gets the path from root to a specific node.
Parameters:
nodeId (int): ID of the target node
rockTreeRootId (int, optional): ID of the tree root to use for path
Response: RockNodePathDto object
Get Nodes by Rock Type
HTTP Method: GET
Endpoint: /api/services/app/RockTreeTraversalService/GetNodesByRockTypeId
Description: Gets all nodes associated with a specific rock type.
Parameters:
rockTypeId (int): ID of the rock type
rockTreeRootId (int, optional): Limit results to a specific tree
includeInactive (boolean, optional): Include inactive nodes
Response: List of RockNodeDto objects
Multi-Tenancy Considerations
When working with the Rock Tree API in a multi-tenant environment, keep these guidelines in mind:

Automatic Tenant Filtering: All API requests are automatically filtered by the current tenant. You don't need to add tenant filtering in your frontend code.

Cross-Tenant Access Prevention: The API prevents access to resources belonging to other tenants. Attempting to access or modify resources from another tenant will result in authorization errors.

New Resource Creation: When creating new resources, the tenant ID is automatically set to the current tenant. There's no need to specify it in requests.

Transparent to Frontend: Multi-tenancy is largely transparent to frontend applications. Your application will only see resources belonging to the current tenant.

Tree Integrity: Tree relationships are maintained within tenant boundaries. Traversal operations will never cross tenant boundaries.

Common Usage Patterns
Creating a Rock Classification System
Create a RockTreeRoot (/api/services/app/RockTreeService/Create)
Create top-level folder nodes (/api/services/app/RockNodeService/CreateFolderNode)
Create rock type nodes under appropriate folders (/api/services/app/RockNodeService/CreateRockTypeNode)
Navigating Trees
Get children of root or folder (/api/services/app/RockTreeTraversalService/GetChildren)
Get path to specific node (/api/services/app/RockTreeTraversalService/GetPath)
Get ancestors or descendants as needed (/api/services/app/RockTreeTraversalService/GetAncestors or /api/services/app/RockTreeTraversalService/GetDescendants)
Rearranging Nodes
Move node to different parent (/api/services/app/RockNodeService/MoveNode)
Reorder siblings under same parent (/api/services/app/RockNodeService/ReorderSiblingNodes)
Clone node or subtree (/api/services/app/RockNodeService/CloneNode)
Finding Related Nodes
Get nodes by rock type (/api/services/app/RockTreeTraversalService/GetNodesByRockTypeId)
Get descendants of a category node (/api/services/app/RockTreeTraversalService/GetDescendants)