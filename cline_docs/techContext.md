# Technical Context

## Technologies

1. Backend Framework

   - .NET 8
   - ASP.NET Core
   - Entity Framework Core

2. Database

   - Likely SQL Server (based on EF Core usage)
   - Code-first migrations

3. Container Technology

   - Docker
   - Docker Compose

4. Development Tools
   - Visual Studio / VS Code
   - Git for version control
   - NuGet package management

## Development Setup

1. Project Structure

   - Solution file: aibase.sln
   - Multiple project layers
   - Separate test projects

2. Build Configuration

   - PowerShell and Shell build scripts
   - Docker build support
   - NuGet package configuration

3. Testing
   - Test projects for core and web
   - Separate test configurations

## Technical Constraints

1. Database

   - Must support multi-tenancy
   - Efficient handling of large datasets
   - Complex relationships between entities

2. Performance

   - Image processing requirements
   - AI/ML service integration
   - Data export performance

3. Security

   - Multi-tenant data isolation
   - Role-based access control
   - API key management

4. Scalability
   - Containerized deployment
   - Stateless application design
   - Resource-intensive operations handling
