# Rock Tree API Documentation

## Introduction

The Rock Tree API provides endpoints for managing hierarchical rock type classifications in the aibase system. This API allows frontend developers to create, manage, and traverse tree structures that organize rock types in a flexible way.

Key features of the Rock Tree API:
- Support for multiple classification hierarchies
- Flexible folder-based organization
- Multiple inheritance (the same rock type can appear in multiple places)
- Tree traversal operations (ancestors, descendants, paths)
- **Multi-tenancy support** - all Rock Tree resources are automatically scoped to the current tenant

> **Multi-Tenancy Note**:
> - All Rock Tree resources (roots, nodes, and relationships) are tenant-specific
> - The current tenant is automatically determined from AbpSession
> - Resources are filtered by tenant without requiring explicit tenant ID parameters
> - Cross-tenant access attempts will result in authorization errors

> **Important Note on Icon Handling**:
> - The `iconUrl` property in RockNode can be null.
> - Frontend applications only need to know the `rockType` to display the correct icon.
> - The frontend is responsible for determining and displaying appropriate icons based on the `rockType`.

All API endpoints follow this URL pattern:
```
/api/services/app/[ServiceName]/[MethodName]
```

## Tree Management

These endpoints manage the Rock Tree roots, which represent different classification systems.

### Create Rock Tree Root
- **Method:** POST
- **Endpoint:** `/api/services/app/RockTreeService/Create`
- **Description:** Creates a new rock classification tree root
- **Request Body:**
  ```json
  {
    "name": "Standard Rock Classification",
    "description": "Classification system based on IUGS standard",
    "geologySuiteId": 42,
    "isActive": true
  }
  ```
- **Response:**
  ```json
  {
    "id": 42,
    "tenantId": 1,
    "name": "Standard Rock Classification",
    "description": "Classification system based on IUGS standard",
    "geologySuiteId": 42,
    "isActive": true,
    "creationTime": "2025-05-20T12:00:00.000Z",
    "creatorUserId": 1,
    "lastModificationTime": "2025-05-20T12:00:00.000Z",
    "lastModifierUserId": 1
  }
  ```
  > Note: The `tenantId` field is included in the response but frontend applications typically don't need to use it as the system automatically handles tenant isolation.

### Get Rock Tree Root
- **Method:** GET
- **Endpoint:** `/api/services/app/RockTreeService/Get`
- **Description:** Gets a rock tree root by ID
- **Parameters:**
  - `id` (int): The ID of the rock tree root
- **Response:** A RockTreeRootDto object

### Get All Rock Tree Roots
- **Method:** GET
- **Endpoint:** `/api/services/app/RockTreeService/GetAll`
- **Description:** Gets a paged list of rock tree roots
- **Parameters:**
  - `SkipCount` (int, optional): Number of items to skip
  - `MaxResultCount` (int, optional): Maximum number of results
  - `Keyword` (string, optional): Search keyword
  - `IsActive` (boolean, optional): Filter by active status
- **Response:** A paged list of RockTreeRootDto objects

### Update Rock Tree Root
- **Method:** PUT
- **Endpoint:** `/api/services/app/RockTreeService/Update`
- **Description:** Updates a rock tree root
- **Request Body:**
  ```json
  {
    "id": 42,
    "name": "Updated Rock Classification",
    "description": "Updated description",
    "isActive": true
  }
  ```
- **Response:** The updated RockTreeRootDto object

### Delete Rock Tree Root
- **Method:** DELETE
- **Endpoint:** `/api/services/app/RockTreeService/Delete`
- **Description:** Deletes a rock tree root
- **Parameters:**
  - `id` (int): The ID of the rock tree root
- **Response:** None

### Associate Geology Suite
- **Method:** POST
- **Endpoint:** `/api/services/app/RockTreeService/AssociateGeologySuite`
- **Description:** Associates a rock tree root with a geology suite
- **Request Body:**
  ```json
  {
    "rockTreeRootId": 42,
    "geologySuiteId": 101
  }
  ```
- **Response:** None

### Disassociate Geology Suite
- **Method:** POST
- **Endpoint:** `/api/services/app/RockTreeService/DisassociateGeologySuite`
- **Description:** Disassociates a rock tree root from a geology suite
- **Request Body:**
  ```json
  {
    "rockTreeRootId": 42
  }
  ```
- **Response:** None

## Node Management

These endpoints manage RockNode entities, which are the building blocks of the tree structure.

### Create Node (Generic)
- **Method:** POST
- **Endpoint:** `/api/services/app/RockNodeService/Create`
- **Description:** Creates a generic rock node
- **Request Body:**
  ```json
  {
    "name": "Node Name",
    "code": "NODE-001",
    "description": "Node description",
    "nodeType": 0, // 0=Folder, 1=ConcreteRockType, 2=VirtualRockType
    "rockTypeId": null, // Required for node types 1 and 2
    "parentNodeId": 24, // Either parentNodeId or rockTreeRootId required
    "rockTreeRootId": 0, // Set if this is a root-level node
    "isActive": true,
    "displayColor": "#3498db",
    "iconUrl": "https://example.com/icon.png", // This can be null; frontend determines icons based on nodeType
    "order": 0 // Display order among siblings
  }
  ```
- **Response:** The created RockNodeDto object, which includes a `tenantId` field that is automatically set based on the current session

### Create Folder Node
- **Method:** POST
- **Endpoint:** `/api/services/app/RockNodeService/CreateFolderNode`
- **Description:** Creates a folder-type node (shorthand for Create with nodeType=0)
- **Request Body:**
  ```json
  {
    "name": "Igneous Rocks",
    "description": "Rocks formed through cooling of magma",
    "parentNodeId": 24, // Either parentNodeId or rockTreeRootId required
    "rockTreeRootId": 0, // Set if this is a root-level node
    "isActive": true,
    "displayColor": "#e67e22",
    "iconUrl": "https://example.com/folder.png", // Optional; frontend should use nodeType for icon display
    "order": 0 // Display order among siblings
  }
  ```
- **Response:** The created RockNodeDto object

### Create Rock Type Node
- **Method:** POST
- **Endpoint:** `/api/services/app/RockNodeService/CreateRockTypeNode`
- **Description:** Creates a node that references a specific rock type
- **Request Body:**
  ```json
  {
    "name": "Granite",
    "code": "IGN-GR",
    "description": "Coarse-grained intrusive igneous rock",
    "rockTypeId": 101, // Required - references the actual rock type
    "parentNodeId": 24, // Either parentNodeId or rockTreeRootId required
    "rockTreeRootId": 0, // Set if this is a root-level node
    "isActive": true,
    "displayColor": "#fd7e14",
    "iconUrl": "https://example.com/rock.png", // Can be null; frontend should use nodeType for icon
    "order": 0 // Display order among siblings
  }
  ```
- **Response:** The created RockNodeDto object

### Get Node
- **Method:** GET
- **Endpoint:** `/api/services/app/RockNodeService/Get`
- **Description:** Gets a node by ID
- **Parameters:**
  - `id` (int): The ID of the node
- **Response:** A RockNodeDto object

### Get All Nodes
- **Method:** GET
- **Endpoint:** `/api/services/app/RockNodeService/GetAll`
- **Description:** Gets a paged and filtered list of nodes
- **Parameters:**
  - `SkipCount` (int, optional): Number of items to skip
  - `MaxResultCount` (int, optional): Maximum number of results
  - `Keyword` (string, optional): Search keyword
  - `IsActive` (boolean, optional): Filter by active status
  - `NodeType` (int, optional): Filter by node type
  - `RockTypeId` (int, optional): Filter by rock type ID
  - `RockTreeRootId` (int, optional): Filter by rock tree root ID
  - `ParentNodeId` (int, optional): Filter by parent node ID
- **Response:** A paged list of RockNodeDto objects

### Update Node
- **Method:** PUT
- **Endpoint:** `/api/services/app/RockNodeService/Update`
- **Description:** Updates a rock node
- **Request Body:**
  ```json
  {
    "id": 42,
    "name": "Updated Node Name",
    "code": "UPD-001",
    "description": "Updated description",
    "nodeType": 0,
    "rockTypeId": null,
    "isActive": true,
    "displayColor": "#3498db",
    "iconUrl": "https://example.com/icon.png" // Optional; frontend determines icons from rockType
  }
  ```
- **Response:** The updated RockNodeDto object

### Delete Node
- **Method:** DELETE
- **Endpoint:** `/api/services/app/RockNodeService/Delete`
- **Description:** Deletes a rock node
- **Parameters:**
  - `id` (int): The ID of the node
- **Response:** None

## Node Relationship Operations

These endpoints modify the relationships between nodes.

### Clone Node
- **Method:** POST
- **Endpoint:** `/api/services/app/RockNodeService/CloneNode`
- **Description:** Clones a node and optionally its descendants
- **Request Body:**
  ```json
  {
    "nodeIdToClone": 42,
    "newName": "Cloned Node",
    "targetRockTreeRootId": 5,
    "newParentNodeId": 24,
    "includeDescendants": true
  }
  ```
- **Response:** The newly cloned RockNodeDto object

### Move Node
- **Method:** POST
- **Endpoint:** `/api/services/app/RockNodeService/MoveNode`
- **Description:** Moves a node to a new parent
- **Request Body:**
  ```json
  {
    "nodeIdToMove": 42,
    "newParentNodeId": 24,
    "newOrder": 0
  }
  ```
- **Response:** The moved RockNodeDto object

### Reorder Sibling Nodes
- **Method:** POST
- **Endpoint:** `/api/services/app/RockNodeService/ReorderSiblingNodes`
- **Description:** Reorders sibling nodes under a common parent
- **Request Body:**
  ```json
  {
    "parentNodeId": 24,
    "orderedNodeIds": [42, 43, 44, 45]
  }
  ```
- **Response:** None

### Activate Node
- **Method:** POST
- **Endpoint:** `/api/services/app/RockNodeService/ActivateNode`
- **Description:** Activates a node
- **Parameters:**
  - `nodeId` (int): The ID of the node
- **Response:** None

### Deactivate Node
- **Method:** POST
- **Endpoint:** `/api/services/app/RockNodeService/DeactivateNode`
- **Description:** Deactivates a node
- **Parameters:**
  - `nodeId` (int): The ID of the node
- **Response:** None

## Tree Traversal

These endpoints help navigate through the tree structure.

### Get Descendants
- **Method:** GET
- **Endpoint:** `/api/services/app/RockTreeTraversalService/GetDescendants`
- **Description:** Gets all descendants of a node
- **Parameters:**
  - `nodeId` (int): The ID of the node
  - `includeInactive` (boolean, optional): Whether to include inactive nodes
- **Response:** A list of RockNodeDto objects

### Get Ancestors
- **Method:** GET
- **Endpoint:** `/api/services/app/RockTreeTraversalService/GetAncestors`
- **Description:** Gets all ancestors of a node
- **Parameters:**
  - `nodeId` (int): The ID of the node
  - `includeInactive` (boolean, optional): Whether to include inactive nodes
- **Response:** A list of RockNodeDto objects

### Get Path
- **Method:** GET
- **Endpoint:** `/api/services/app/RockTreeTraversalService/GetPath`
- **Description:** Gets the path to a node
- **Parameters:**
  - `nodeId` (int): The ID of the node
  - `rockTreeRootId` (int, optional): The ID of the rock tree root
- **Response:** A RockNodePathDto object containing the path and target node

### Get Children
- **Method:** GET
- **Endpoint:** `/api/services/app/RockTreeTraversalService/GetChildren`
- **Description:** Gets direct children of a node
- **Parameters:**
  - `nodeId` (int): The ID of the node
  - `includeInactive` (boolean, optional): Whether to include inactive nodes
- **Response:** A list of RockNodeDto objects

### Get Nodes By Category
- **Method:** GET
- **Endpoint:** `/api/services/app/RockTreeTraversalService/GetNodesByCategory`
- **Description:** Gets nodes by rock type category
- **Parameters:**
  - `categoryId` (int): The ID of the category
  - `rockTreeRootId` (int, optional): The ID of the rock tree root
  - `includeInactive` (boolean, optional): Whether to include inactive nodes
- **Response:** A list of RockNodeDto objects

### Get Nodes By Rock Type
- **Method:** GET
- **Endpoint:** `/api/services/app/RockTreeTraversalService/GetNodesByRockTypeId`
- **Description:** Gets nodes by rock type
- **Parameters:**
  - `rockTypeId` (int): The ID of the rock type
  - `rockTreeRootId` (int, optional): The ID of the rock tree root
  - `includeInactive` (boolean, optional): Whether to include inactive nodes
- **Response:** A list of RockNodeDto objects

## Common Usage Patterns

### Creating a New Rock Classification System

1. Create a rock tree root using `POST /api/services/app/RockTreeService/Create`
2. Create folder nodes at the root level using `POST /api/services/app/RockNodeService/CreateFolderNode`
3. Add rock type nodes under folders using `POST /api/services/app/RockNodeService/CreateRockTypeNode`

### Navigating a Tree Structure

1. Get root-level nodes by filtering with `GET /api/services/app/RockNodeService/GetAll?RockTreeRootId=42`
2. Get children of a specific node with `GET /api/services/app/RockTreeTraversalService/GetChildren?nodeId=24`
3. Get the full path to a node with `GET /api/services/app/RockTreeTraversalService/GetPath?nodeId=42`

### Rearranging Nodes

1. Move a node to a different parent with `POST /api/services/app/RockNodeService/MoveNode`
2. Reorder sibling nodes with `POST /api/services/app/RockNodeService/ReorderSiblingNodes`
3. Clone subtrees with `POST /api/services/app/RockNodeService/CloneNode`

### Finding Related Nodes

1. Find all instances of a rock type with `GET /api/services/app/RockTreeTraversalService/GetNodesByRockTypeId?rockTypeId=101`
2. Get all ancestors of a node with `GET /api/services/app/RockTreeTraversalService/GetAncestors?nodeId=42`
3. Get all descendants of a node with `GET /api/services/app/RockTreeTraversalService/GetDescendants?nodeId=24`

## Multi-Tenancy Considerations

### Tenant Isolation

- All Rock Tree data is automatically isolated by tenant
- API calls only return data belonging to the current tenant
- Tenant ID is determined from the authentication token (AbpSession)
- You cannot access Rock Tree data from other tenants

### Working with Rock Tree API in Multi-Tenant Environments

1. **Authentication**: Ensure users are properly authenticated with the correct tenant context
2. **No Explicit Tenant IDs**: You don't need to pass tenant IDs in requests - the framework handles this automatically
3. **Response Handling**: Response objects include `tenantId` fields, but your frontend code typically doesn't need to use these values
4. **Entity Creation**: When creating entities, you don't need to specify a tenant ID - it's set automatically
5. **Migration Considerations**: If you need to migrate Rock Tree data between tenants, this must be handled by backend administrators using specialized tools

### Common Multi-Tenant Patterns

- Creating separate Rock Tree hierarchies for each tenant
- Customizing Rock Type structures per tenant while maintaining consistent rock type definitions
- Setting up tenant-specific naming conventions and organizational structures